package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 对接产品API接口实体类
 *
 * <AUTHOR>
 */
@Data
@ApiModel("对接产品API接口")
@TableName("api_interface")
public class ApiInterface implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联api版本记录ID
     */
    @ApiModelProperty("关联api版本记录ID")
    private Long releaseId;
    
    /**
     * 接口名
     */
    @ApiModelProperty("接口名")
    private String apiName;
    
    /**
     * 接口请求方式
     */
    @ApiModelProperty("接口请求方式")
    private String apiMethod;
    
    /**
     * 接口路径
     */
    @ApiModelProperty("接口路径")
    private String apiPath;

    /**
     * 接口类型颗粒度：1-验证能力；2-具体结果
     */
    @ApiModelProperty("接口类型颗粒度：1-验证能力；2-具体结果")
    private Integer apiType;
    
    /**
     * 请求参数
     */
    @ApiModelProperty("请求参数")
    private String reqParams;
    
    /**
     * 请求结果
     */
    @ApiModelProperty("请求结果")
    private String reqResult;
    
    /**
     * 接口排序
     */
    @ApiModelProperty("接口排序")
    private Integer sort;
    
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private String createUser;
    
    /**
     * 修改用户
     */
    @ApiModelProperty("修改用户")
    private String updateUser;
}
