Do $$
BEGIN
BEGIN
ALTER TABLE public.co_progress ADD update_time timestamp(6) NULL;
EXCEPTION
    WHEN duplicate_column THEN RAISE NOTICE 'column update_time already exists in co_progress.';
END;
END;
$$;
COMMENT ON COLUMN public.co_progress.update_time IS '更新时间';
-- =============================================
-- 工作流任务系统相关表结构
-- =============================================

-- 1. 工作流任务主表
create table if not exists workflow_task
(
    id                  bigserial
        constraint workflow_task_pk
            primary key,
    operation_id        varchar(64),
    name                varchar(255) not null,
    capability          varchar(255) not null,
    product_id          bigint       not null,
    product_name        varchar(255) not null,
    logo                text,
    company             varchar(255),
    task_type           integer      not null,
    task_config         text,
    current_step        integer,
    total_steps         integer,
    progress_percentage integer,
    error_message       text,
    execution_result    text,
    verify_result       varchar(100),
    status              integer      not null,
    start_time          timestamp(6),
    end_time            timestamp(6),
    execution_duration  integer,
    create_time         timestamp(6),
    create_by           varchar(50),
    update_time         timestamp(6),
    update_by           varchar(50)
);

comment on table workflow_task is '工作流任务表';
comment on column workflow_task.id is '主键';
comment on column workflow_task.name is '任务名称';
comment on column workflow_task.capability is '产品能力id';
comment on column workflow_task.product_id is '产品id';
comment on column workflow_task.product_name is '产品名称';
comment on column workflow_task.logo is '产品logo';
comment on column workflow_task.task_type is '任务类型 1-能力验证 2-展示分类分级结果';
comment on column workflow_task.task_config is '任务配置参数';
comment on column workflow_task.current_step is '当前步骤';
comment on column workflow_task.total_steps is '总步骤数';
comment on column workflow_task.progress_percentage is '执行进度';
comment on column workflow_task.error_message is '错误信息';
comment on column workflow_task.execution_result is '执行结果';
comment on column workflow_task.verify_result is '验证结果';
comment on column workflow_task.status is '状态 0-待执行, 1-执行中, 2-已完成, 3-失败, 4-已终止';
comment on column workflow_task.start_time is '开始时间';
comment on column workflow_task.end_time is '结束时间';
comment on column workflow_task.execution_duration is '执行时长（毫秒）';
comment on column workflow_task.create_time is '创建时间';
comment on column workflow_task.create_by is '创建人';
comment on column workflow_task.update_time is '更新时间';
comment on column workflow_task.update_by is '更新人';
alter table workflow_task
    owner to dcas;

-- 2. 任务步骤表
CREATE TABLE IF NOT EXISTS task_step (
    id bigserial
        constraint task_step_pk
            primary key,
    task_id bigint NOT NULL,
    interface_id bigint NOT NULL,
    step_order integer NOT NULL,
    step_name varchar(100) NOT NULL,
    step_description varchar(500),
    status integer NOT NULL DEFAULT 0,
    api_endpoint varchar(500) NOT NULL,
    http_method varchar(10) NOT NULL,
    request_headers text,
    request_params text,
    response_format text,
    timeout_seconds integer DEFAULT 30,
    retry_count integer DEFAULT 0,
    max_retries integer DEFAULT 3,
    required Integer DEFAULT 1,
    continue_on_failure Integer DEFAULT 0,
    start_time timestamp(6),
    end_time timestamp(6),
    execution_duration bigint,
    response_status integer,
    response_data text,
    error_message text,
    output_data text,
    create_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by varchar(64) NOT NULL
    );

-- 添加表注释
COMMENT ON TABLE task_step IS '任务步骤表';
COMMENT ON COLUMN task_step.id IS '步骤ID';
COMMENT ON COLUMN task_step.task_id IS '任务ID';
COMMENT ON COLUMN task_step.interface_id IS '接口ID';
COMMENT ON COLUMN task_step.step_order IS '步骤序号';
COMMENT ON COLUMN task_step.step_name IS '步骤名称';
COMMENT ON COLUMN task_step.step_description IS '步骤描述';
COMMENT ON COLUMN task_step.status IS '步骤状态：PENDING-待执行, RUNNING-执行中, COMPLETED-已完成, FAILED-失败, SKIPPED-已跳过';
COMMENT ON COLUMN task_step.api_endpoint IS 'API端点URL';
COMMENT ON COLUMN task_step.http_method IS 'HTTP方法：GET, POST, PUT, DELETE';
COMMENT ON COLUMN task_step.request_headers IS '请求头配置JSON';
COMMENT ON COLUMN task_step.request_params IS '请求参数配置JSON';
COMMENT ON COLUMN task_step.response_format IS '响应格式JSON';
COMMENT ON COLUMN task_step.timeout_seconds IS '超时时间（秒）';
COMMENT ON COLUMN task_step.retry_count IS '重试次数';
COMMENT ON COLUMN task_step.max_retries IS '最大重试次数';
COMMENT ON COLUMN task_step.required IS '是否必须成功：0-否, 1-是';
COMMENT ON COLUMN task_step.continue_on_failure IS '失败时是否继续：0-否, 1-是';
COMMENT ON COLUMN task_step.start_time IS '开始时间';
COMMENT ON COLUMN task_step.end_time IS '结束时间';
COMMENT ON COLUMN task_step.execution_duration IS '执行时长（毫秒）';
COMMENT ON COLUMN task_step.response_status IS '响应状态码';
COMMENT ON COLUMN task_step.response_data IS '响应数据';
COMMENT ON COLUMN task_step.error_message IS '错误信息';
COMMENT ON COLUMN task_step.output_data IS '输出数据（传递给下一步的数据）';
COMMENT ON COLUMN task_step.create_time IS '创建时间';
COMMENT ON COLUMN task_step.create_by IS '创建者';

-- 3. 任务执行记录表
CREATE TABLE IF NOT EXISTS task_execution (
    execution_id bigserial
        constraint task_execution_pk
            PRIMARY KEY,
    task_id bigint NOT NULL,
    execution_batch varchar(64),
    status varchar(20) NOT NULL DEFAULT 'STARTED',
    start_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time timestamp(6),
    execution_duration bigint,
    current_step integer DEFAULT 0,
    total_steps integer DEFAULT 0,
    progress_percentage integer DEFAULT 0,
    success_steps integer DEFAULT 0,
    failed_steps integer DEFAULT 0,
    skipped_steps integer DEFAULT 0,
    error_message text,
    execution_log text,
    execution_result text,
    execution_environment text,
    executor_id bigint,
    executor_name varchar(64),
    manually_terminated varchar(1) DEFAULT '0',
    termination_reason varchar(500),
    retry_count integer DEFAULT 0,
    parent_execution_id bigint
    );

-- 添加表注释
COMMENT ON TABLE task_execution IS '任务执行记录表';
COMMENT ON COLUMN task_execution.execution_id IS '执行记录ID';
COMMENT ON COLUMN task_execution.task_id IS '任务ID';
COMMENT ON COLUMN task_execution.execution_batch IS '执行批次号';
COMMENT ON COLUMN task_execution.status IS '执行状态：STARTED-已开始, RUNNING-执行中, COMPLETED-已完成, FAILED-失败, TERMINATED-已终止';
COMMENT ON COLUMN task_execution.start_time IS '开始时间';
COMMENT ON COLUMN task_execution.end_time IS '结束时间';
COMMENT ON COLUMN task_execution.execution_duration IS '执行时长（毫秒）';
COMMENT ON COLUMN task_execution.current_step IS '当前执行步骤';
COMMENT ON COLUMN task_execution.total_steps IS '总步骤数';
COMMENT ON COLUMN task_execution.progress_percentage IS '执行进度百分比';
COMMENT ON COLUMN task_execution.success_steps IS '成功步骤数';
COMMENT ON COLUMN task_execution.failed_steps IS '失败步骤数';
COMMENT ON COLUMN task_execution.skipped_steps IS '跳过步骤数';
COMMENT ON COLUMN task_execution.error_message IS '错误信息';
COMMENT ON COLUMN task_execution.execution_log IS '执行日志';
COMMENT ON COLUMN task_execution.execution_result IS '执行结果';
COMMENT ON COLUMN task_execution.execution_environment IS '执行环境信息';
COMMENT ON COLUMN task_execution.executor_id IS '执行者ID';
COMMENT ON COLUMN task_execution.executor_name IS '执行者名称';
COMMENT ON COLUMN task_execution.manually_terminated IS '是否手动终止：0-否, 1-是';
COMMENT ON COLUMN task_execution.termination_reason IS '终止原因';
COMMENT ON COLUMN task_execution.retry_count IS '重试次数';
COMMENT ON COLUMN task_execution.parent_execution_id IS '父执行记录ID（重试时关联原执行记录）';

CREATE TABLE IF NOT EXISTS api_integrated_product
(
    id                BIGSERIAL
        CONSTRAINT api_integrated_product_pk
            PRIMARY KEY,
    release_id        BIGINT       NOT NULL,
    name              VARCHAR(100) NOT NULL,
    url               VARCHAR(255) NOT NULL,
    capability        VARCHAR(255) NOT NULL,
    product_name      VARCHAR(100) NOT NULL,
    company           VARCHAR(255) NOT NULL,
    logo              TEXT,
    status            VARCHAR(50)  NOT NULL DEFAULT 'NORMAL_CONNECTION',
    config_params     TEXT,
    error_message     TEXT,
    version           INTEGER,
    create_by         VARCHAR(50)  NOT NULL,
    create_time       TIMESTAMP(6) NOT NULL,
    update_by         VARCHAR(50),
    update_time       TIMESTAMP(6)
);

-- 添加表注释
COMMENT ON TABLE api_integrated_product IS '集成产品表，用于存储已集成的第三方产品信息及其状态';

-- 添加字段注释
COMMENT ON COLUMN api_integrated_product.id IS '主键ID';
COMMENT ON COLUMN api_integrated_product.release_id IS '关联API版本记录ID';
COMMENT ON COLUMN api_integrated_product.name IS '产品名称';
COMMENT ON COLUMN api_integrated_product.url IS '产品地址';
COMMENT ON COLUMN api_integrated_product.capability IS '产品能力';
COMMENT ON COLUMN api_integrated_product.product_name IS '产品类型名称';
COMMENT ON COLUMN api_integrated_product.company IS '所属公司';
COMMENT ON COLUMN api_integrated_product.logo IS '产品logo';
COMMENT ON COLUMN api_integrated_product.status IS '集成状态：NORMAL_CONNECTION-正常连接，CONNECTION_ERROR-连接错误，PARAMETER_UPDATED-参数已更新，DELETED_FROM_KNOWLEDGE_BASE-已从知识库删除';
COMMENT ON COLUMN api_integrated_product.config_params IS '配置参数（JSON格式）';
COMMENT ON COLUMN api_integrated_product.error_message IS '错误信息';
COMMENT ON COLUMN api_integrated_product.version IS '版本号';
COMMENT ON COLUMN api_integrated_product.create_by IS '创建人';
COMMENT ON COLUMN api_integrated_product.create_time IS '创建时间';
COMMENT ON COLUMN api_integrated_product.update_by IS '修改人';
COMMENT ON COLUMN api_integrated_product.update_time IS '修改时间';

INSERT INTO library_sync_tables (table_name) VALUES ('api_release') ON conflict("table_name") do nothing;
INSERT INTO library_sync_tables (table_name) VALUES ('api_interface') ON conflict("table_name") do nothing;


Do $$
BEGIN
BEGIN
ALTER TABLE public.security_operation ADD del_flag bpchar(1) DEFAULT '0' NOT NULL;
EXCEPTION
    WHEN duplicate_column THEN RAISE NOTICE 'column del_flag already exists in security_operation.';
END;
END;
$$;
COMMENT ON COLUMN public.security_operation.del_flag IS '删除标志';

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (8, '产品对接管理', 0, 6, '/productDocking', '@/pages/productDocking', '', '1', '0', 'C', '0', '0', e'productDocking:list', 'product', 'admin', now(), '', null, '产品对接管理') ON CONFLICT ("menu_id") DO NOTHING;
INSERT INTO public.sys_role_menu (role_id, menu_id) VALUES (4, 8) ON conflict("role_id","menu_id") do nothing;

UPDATE public."co_process_tree" SET order_num = 4 WHERE tree_id = 103;
UPDATE public."co_process_tree" SET order_num = 5  WHERE tree_id = 104;
UPDATE public."co_process_tree" SET order_num = 6  WHERE tree_id = 105;
-- 技术能力检测
INSERT INTO public.co_process_tree (tree_id, parent_id, ancestors, tree_name, order_num, status, del_flag, create_by, create_time, update_by, update_time) VALUES (118, 100, '0,100', '技术能力检测', 3, '0', '0', 'admin', now(), '', now()) ON conflict("tree_id") do nothing;
