package com.dcas.common.model.other;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/10 11:38
 * @since 1.0.0
 */
@Data
public class InParam {
    private List<ColumnParam> columnList;

    @Data
    public static class ColumnParam {
        @ApiModelProperty("字段名")
        private String columnName;

        @ApiModelProperty("字段注释")
        private String columnComment;

        @ApiModelProperty("数据来源:0-默认值; 1-终端输入; 2-接口结果")
        private Integer type;

        @ApiModelProperty("具体内容")
        private String value;

        @ApiModelProperty("层级")
        private Integer level;

        @ApiModelProperty("字段类型")
        private String columnType;

        @ApiModelProperty("配置类型：1-文本框；2-文本框加密；3-下拉列表单选；4-下拉列表多选")
        private Integer configType;

        @ApiModelProperty("配置可选值")
        private List<KeyValue> configValue;

        @ApiModelProperty("字段定位,用/分割")
        private String location;
    }
}
