package com.dcas.system.service;

import com.dcas.common.utils.PageResult;
import com.dcas.common.domain.entity.AnalysisTemplate;
import com.dcas.common.model.param.AnalysisTempParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.TemplateSearchReq;
import com.dcas.common.model.vo.AnalysisTempVO;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/6 16:49
 * @since 1.2.0
 */
public interface AnalysisTemplateService {

    PageResult<AnalysisTemplate> list(Integer currentPage, Integer pageSize);

    void add(AnalysisTempParam param);

    void update(AnalysisTempParam param);

    void delete(IdsReq req);

    void setDefault(Integer id);

    void enable(Integer id);

    void createBak(AnalysisTempParam param);

    AnalysisTempVO details(Integer id);

    List<AnalysisTemplate> search(TemplateSearchReq req);
}
