package com.dcas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *     接口权限类型配置枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/9 14:40
 * @since *******
 */
public class AuthConstants {

    @Getter
    @AllArgsConstructor
    public enum ParamType {
        USER_INPUT("用户输入"),
        SYSTEM_AUTO("系统自动获取"),
        ALGORITHM("算法计算");

        private final String description;
    }

    @Getter
    @AllArgsConstructor
    public enum SystemAutoType {
        TIMESTAMP_MILLIS("毫秒级时间戳"),
        TIMESTAMP_SECONDS("秒级时间戳"),
        UUID("UUID"),
        RANDOM_STRING("随机字符串");

        private final String description;
    }

    @Getter
    @AllArgsConstructor
    public enum AlgorithmType {
        MD5("MD5加密"),
        SHA256("SHA256加密"),
        BASE64("Base64编码");

        private final String description;
    }
}
