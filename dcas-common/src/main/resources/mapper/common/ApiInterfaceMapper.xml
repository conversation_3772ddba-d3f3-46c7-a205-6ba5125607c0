<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.ApiInterfaceMapper">
    <select id="selectByReleaseId" resultType="com.dcas.common.domain.entity.ApiInterface">
        select id, release_id, api_name, api_method, api_path, req_params, req_result, sort
        from api_interface
        where release_id = #{releaseId}
        order by sort
    </select>
    <select id="selectByReleaseIdAndTaskType" resultType="com.dcas.common.domain.entity.ApiInterface">
        select id, release_id, api_name, api_method, api_path, req_params, req_result, sort
        from api_interface
        where release_id = #{releaseId} and api_type = #{taskType}
        order by sort
    </select>
    <select id="selectApiTypeByReleaseId" resultType="java.lang.String">
        SELECT string_agg(api_type::text, ',') AS concatenated_types
        FROM (SELECT DISTINCT api_type
              FROM api_interface
              where release_id = #{releaseId}
              ORDER BY api_type) AS distinct_types;
    </select>
</mapper>