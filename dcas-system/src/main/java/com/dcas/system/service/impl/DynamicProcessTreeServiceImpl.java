package com.dcas.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.DynamicProcessTree;
import com.dcas.common.mapper.DynamicProcessTreeMapper;
import com.dcas.system.service.DynamicProcessTreeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/4 10:53
 * @since 1.4.0
 */
@Service
@RequiredArgsConstructor
public class DynamicProcessTreeServiceImpl extends ServiceImpl<DynamicProcessTreeMapper, DynamicProcessTree>
        implements DynamicProcessTreeService {

    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;

    @Override
    public List<Long> selectTreeIdBySrc(String operationId, Long srcTreeId) {
        return dynamicProcessTreeMapper.selectTreeIdBySrc(operationId, srcTreeId);
    }
}
