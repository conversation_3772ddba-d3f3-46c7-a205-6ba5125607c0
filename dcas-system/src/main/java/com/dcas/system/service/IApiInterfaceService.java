package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.model.vo.ProductIntegrationVO;

import java.util.List;

/**
 * API接口服务接口
 *
 * <AUTHOR>
 */
public interface IApiInterfaceService extends IService<ApiInterface> {
    
    /**
     * 根据API版本记录ID查询接口列表
     *
     * @param releaseId API版本记录ID
     * @return 接口列表
     */
    List<ApiInterface> getByReleaseId(Long releaseId, Integer taskType);
}
