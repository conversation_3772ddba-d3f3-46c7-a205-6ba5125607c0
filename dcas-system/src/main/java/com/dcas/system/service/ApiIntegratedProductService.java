package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.domain.entity.ApiIntegratedProduct;
import com.dcas.common.model.req.CreateIntegrationReq;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.UpdateIntegrationReq;
import com.dcas.common.model.vo.AbilityModelVO;
import com.dcas.common.model.vo.ProductInfoVO;
import com.dcas.common.model.vo.ProductIntegrationVO;

import java.util.List;

/**
 * 集成产品服务接口
 *
 * <AUTHOR>
 */
public interface ApiIntegratedProductService extends IService<ApiIntegratedProduct> {
    
    /**
     * 分页查询集成产品列表
     *
     * @return 结果
     */
    List<ProductIntegrationVO> getIntegrationPage(String capability);
    
    /**
     * 创建新的产品集成
     *
     * @param request 创建请求
     */
    void createIntegration(CreateIntegrationReq request);
    
    /**
     * 更新产品集成
     *
     * @param request 更新请求
     */
    void updateIntegration(UpdateIntegrationReq request);
    
    /**
     * 删除产品集成
     *
     * @param ids 集成产品ID数组
     */
    void deleteIntegrations(IdsReq ids);

    /**
     * 获取产品详情
     *
     * @param id 产品ID
     * @return 产品详情
     */
    ProductInfoVO getProductInfo(Long id);
}
