package com.dcas.common.utils.params;

import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.param.AuthParameter;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *     鉴权参数解析器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/9 16:46
 * @since 2.1.3.0
 */
@Slf4j
@Component
public class AuthParameterParser {
    private final ObjectMapper objectMapper;

    public AuthParameterParser() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 将JSON字符串解析为AuthParameter列表
     * 会根据type字段自动创建对应的子类实例
     *
     * @param jsonString 数据库中保存的JSON字符串
     * @return AuthParameter子类列表
     */
    public List<AuthParameter> parseAuthParameters(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            log.warn("传入的JSON字符串为空");
            return new ArrayList<>();
        }

        try {
            // 使用TypeReference来指定泛型类型，Jackson会根据@JsonTypeInfo注解自动创建子类实例
            TypeReference<List<AuthParameter>> typeReference = new TypeReference<List<AuthParameter>>() {};
            List<AuthParameter> parameters = objectMapper.readValue(jsonString, typeReference);

            log.info("成功解析{}个鉴权参数", parameters.size());
            return parameters;

        } catch (Exception e) {
            log.error("解析AuthParameter JSON失败: {}", e.getMessage(), e);
            throw new ServiceException("解析鉴权参数配置失败", e);
        }
    }

    /**
     * 将AuthParameter列表转换为JSON字符串
     *
     * @param parameters AuthParameter列表
     * @return JSON字符串
     */
    public String toJsonString(List<AuthParameter> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return "[]";
        }

        try {
            return objectMapper.writeValueAsString(parameters);
        } catch (Exception e) {
            log.error("转换AuthParameter为JSON失败: {}", e.getMessage(), e);
            throw new ServiceException("转换鉴权参数配置失败", e);
        }
    }

    /**
     * 根据类型过滤参数
     *
     * @param parameters 参数列表
     * @param targetClass 目标类型
     * @return 指定类型的参数列表
     */
    @SuppressWarnings("unchecked")
    public <T extends AuthParameter> List<T> filterByType(List<AuthParameter> parameters, Class<T> targetClass) {
        List<T> result = new ArrayList<>();

        for (AuthParameter parameter : parameters) {
            if (targetClass.isInstance(parameter)) {
                result.add((T) parameter);
            }
        }

        return result;
    }

    /**
     * 根据key查找参数
     *
     * @param parameters 参数列表
     * @param key 参数key
     * @return 找到的参数，未找到返回null
     */
    public AuthParameter findByKey(List<AuthParameter> parameters, String key) {
        return parameters.stream()
                .filter(param -> key.equals(param.getKey()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取需要放在请求头中的参数
     *
     * @param parameters 参数列表
     * @return 需要放在请求头中的参数列表
     */
    public List<AuthParameter> getHeaderParameters(List<AuthParameter> parameters) {
        return parameters.stream()
                .filter(param -> Boolean.TRUE.equals(param.getInHeader()))
                .collect(java.util.stream.Collectors.toList());
    }
}

