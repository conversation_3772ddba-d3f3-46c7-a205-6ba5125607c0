package com.dcas.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * 分类ID枚举
 *
 * 注意：枚举顺序将是作业结项时标签结果检测的顺序，请有序编写
 */
@Getter
@AllArgsConstructor
public enum LabelEnum {
    WJDY(101L, "问卷调研", 0L),
    ZCSL(102L, "资产梳理", 0L),
    AQXZ(103L, "安全现状", 0L),
    FXPG(104L, "安全评估", 0L),
    AQJY(105L, "安全建议", 0L),
    JSJC(118L, "技术能力检测", 0L),

    ZZDY(106L, "组织调研", 101L),
    XTDY(107L, "系统调研", 101L),
    XZHY(108L, "现状核验", 101L),
    RS(109L, "人事", 101L),

    ZCPD(110L, "资产盘点", 102L),
    SJQX(111L, "数据权限", 102L),

    NLFX(112L, "能力分析", 103L),
    CJFX(113L, "差距分析", 103L),

    JCHJ(114L, "基础评估", 104L),
    HFHG(115L, "合法评估", 104L),
    SMZQ(116L, "风险评估", 104L),

    CZJY(117L, "处置建议", 105L);

    private final Long code;
    private final String name;
    private final Long pCode;

    // 调研对象Map
    public static final Map<String, Long> MAP = new HashMap<>();

    static {
        MAP.put(ZZDY.getName(), ZZDY.getCode());
        MAP.put(XTDY.getName(), XTDY.getCode());
    }

    public static String getNameByCode(Long code) {
        for (LabelEnum labelEnum : LabelEnum.values()) {
            if (code.equals(labelEnum.getCode())) {
                return labelEnum.getName();
            }
        }
        return null;
    }

    public static LabelEnum getEnum(Long code) {
        for (LabelEnum labelEnum : LabelEnum.values()) {
            if (code.equals(labelEnum.getCode())) {
                return labelEnum;
            }
        }
        return null;
    }

    public static List<LabelEnum> getByParentCode(Long code) {
        List<LabelEnum> res = Lists.newArrayList();
        for (LabelEnum labelEnum : LabelEnum.values()) {
            if (code.equals(labelEnum.getPCode())) {
                res.add(labelEnum);
            }
        }
        return res;
    }
}
