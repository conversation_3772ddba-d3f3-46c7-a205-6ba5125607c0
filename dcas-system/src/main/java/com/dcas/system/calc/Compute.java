package com.dcas.system.calc;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dcas.common.enums.OperationEnum;
import com.dcas.system.calc.operator.OperatorFactory;
import com.dcas.system.calc.operator.OperatorInterface;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Compute {

    private final static Logger logger = LoggerFactory.getLogger("risk-calc");

    private static final Pattern PATTERN = Pattern.compile("(?<!\\d)-?\\d+(\\.\\d+)?|[+\\-*/()MCF]");
    private static Map<String, Long> keyMap = new LinkedHashMap<>();
    static {
        keyMap.put("F", OperationEnum.FILTER.getType());
        keyMap.put("M", OperationEnum.MAX.getType());
        keyMap.put("L", OperationEnum.MIN.getType());
        keyMap.put("C", OperationEnum.COUNT.getType());
        keyMap.put("S", OperationEnum.IFS.getType());
        keyMap.put(OperationEnum.AVG.getValue(), OperationEnum.AVG.getType());
        keyMap.put("U", OperationEnum.UP.getType());
        keyMap.put(OperationEnum.SUM.getValue(), OperationEnum.SUM.getType());
        keyMap.put("@", OperationEnum.IN.getType());
        keyMap.put("&", OperationEnum.AND.getType());
        keyMap.put("#", OperationEnum.OR.getType());
    }


    public final static Pattern PATTERN_INNER = Pattern.compile("[MLCFSμUΣ@]");


    /**
     * 获得优先级的方法
     */
    private static int getPriority(String str) throws Exception {
        if (StringUtils.isBlank(str)) {
            return 0;
        }
        switch(str) {
            case "(":
                return 1;
            case "+":
            case "-":
                return 2;
            case "*":
            case "/":
                return 3;
            case "F":
            case "M":
            case "C":
            case "I":
            case "A":
            case "O":
                return 4;
            default:
                break;
        }
        throw new Exception("illegal operator!");
    }

    /**
     *  两位数四则运算方法
     */
    private static BigDecimal doubleCal(BigDecimal a1, BigDecimal a2, char operator) throws Exception {
        switch (operator) {
            case '+':
                return a1.add(a2) ;
            case '-':
                return a1.subtract(a2);
            case '*':
                return a1.multiply(a2);
            case '/':
                if (a2.compareTo(BigDecimal.ZERO) == 0){
                    return BigDecimal.ZERO;
                }
                return a1.divide(a2,20,BigDecimal.ROUND_HALF_UP);
            default:
                break;
        }
        throw new Exception("illegal operator!");
    }


    private static boolean containsLetterOrChinese(String str) {
        // 正则表达式，匹配字母
        String regexLetters = ".*[a-zA-Z]+.*";
        // 正则表达式，匹配中文字符
        String regexChinese = ".*[\u4e00-\u9fa5]+.*";

        return str.matches(regexLetters) || str.matches(regexChinese);
    }


    /**
     * 计算方法
     * @param expr
     * @return
     * @throws Exception
     */
    public static String getResult(String expr) throws Exception {
        // 非计算公式 直接返回
        if (containsLetterOrChinese(expr)){
            return expr;
        }
        logger.info("计算:" + expr);
        Stack<BigDecimal> number = new Stack<>();/*数字栈*/
        Stack<String> operator = new Stack<String>();/*符号栈*/
        // 在栈顶压人一个null，配合它的优先级，目的是减少下面程序的判断
        operator.push(null);
        // 将expr打散为运算数和运算符,这个正则为匹配表达式中的数字或运算符
        Matcher matcher = PATTERN.matcher(
            expr.replace("÷", "/").replace("×", "*").replace("%", "/100").replace("CORE", "").replace("IMPORTANT", "")
                .replace("GENERAL", ""));
        while(matcher.find()) {
            String temp = matcher.group();
            //遇到符号
            if(temp.matches("[+\\-*/()]")) {
                //遇到左括号，直接入符号栈
                if("(".equals(temp)) {
                    operator.push(temp);
                    //log.info("符号栈更新：" + operator);
                }else if(")".equals(temp)){
                    //遇到右括号，"符号栈弹栈取栈顶符号b，数字栈弹栈取栈顶数字a1，数字栈弹栈取栈顶数字a2，计算a2 b a1 ,将结果压入数字栈"，重复引号步骤至取栈顶为左括号，将左括号弹出
                    String str = "";
                    while(!"(".equals((str = operator.pop()))) {
                        BigDecimal a1 = number.pop();
                        BigDecimal a2 = number.pop();
                        //log.info("数字栈更新：" + number + "---计算" + a2 + str + a1);
                        number.push(doubleCal(a2, a1, str.charAt(0)));
                        //log.info("数字栈更新：" + number);
                    }
                    //log.info("符号栈更新：" + operator);
                }else {
                    // 遇到运算符，满足该运算符的优先级大于栈顶元素的优先级压栈；否则计算后压栈
                    while(getPriority(temp) <= getPriority(operator.peek())) {
                        BigDecimal a1 = number.pop();
                        BigDecimal a2 = number.pop();
                        String str1 = operator.pop();
                        //log.info("符号栈更新：" + operator + "--数字栈更新：" + number + "---计算" + a2 + str1 + a1);
                        number.push(doubleCal(a2, a1, str1.charAt(0)));
                        //log.info("数字栈更新：" + number);
                    }
                    operator.push(temp);
                    //log.info("符号栈更新："+operator);
                }
            }else {//遇到数字，直接压入数字栈
                number.push(new BigDecimal(temp));
                //log.info("数字栈更新：" + number);
            }
        }
        //遍历结束后，符号栈数字栈依次弹栈计算，并将结果压入数字栈
        while(operator.peek() != null) {
            BigDecimal a1 = number.empty() ? BigDecimal.ZERO : number.pop();
            BigDecimal a2 = number.empty() ? BigDecimal.ZERO : number.pop();
            String str = operator.pop();
            //log.info("符号栈更新：" + operator + "--数字栈更新：" + number + "--计算" + a2 + str + a1);
            number.push(doubleCal(a2, a1, str.charAt(0)));
            //log.info("数字栈更新：" + number);
        }
        if (number.empty()){
            logger.info("无需计算。");
            return expr;
        }
        BigDecimal pop = number.pop();
        String result;
        if (NumberUtil.isInteger(pop.toString())){
            NumberFormat df = NumberFormat.getIntegerInstance();//精度自己控制保留几位小数点
            df.setGroupingUsed(Boolean.FALSE);
            result = df.format(pop);
        } else if (NumberUtil.isDouble(pop.toString())) {
            NumberFormat df = NumberFormat.getNumberInstance();
            df.setGroupingUsed(Boolean.FALSE);
            result = df.format(pop);
        } else {
            DecimalFormat df = new DecimalFormat("0.0000");//精度自己控制保留几位小数点
            df.setGroupingUsed(Boolean.FALSE);
            result = df.format(pop);
        }
        logger.info("计算完成.结果：{}", result);
        return result;
    }


    public static BigDecimal calculate(String expr, Map<String, Object> stepResultMap){
        logger.info("formula:{}", expr);
        BigDecimal result = BigDecimal.ZERO;
        Stack<String> operator = new Stack<String>();/*符号栈*/
        Stack<Object> number = new Stack<Object>();/*符号栈*/
        // 在栈顶压人一个null，配合它的优先级，目的是减少下面程序的判断
        operator.push(null);
        String exprAfter = replaceExpr(expr);
        String[] exprArr = exprAfter.split("");
        for (String token : exprArr) {
            if ("(".equals(token)) {
                operator.push(token);
            } else if (")".equals(token)) {
                //遇到右括号，"符号栈弹栈取栈顶符号b，数字栈弹栈取栈顶数字a1，数字栈弹栈取栈顶数字a2，计算a2 b a1 ,将结果压入数字栈"，重复引号步骤至取栈顶为左括号，将左括号弹出
                String str = token;
                while (!"(".equals((token = operator.pop()))) {
                    str = CharSequenceUtil.concat(true, token, str);
                }
                str = CharSequenceUtil.concat(true, token, str);
                // 左括号出栈
                String oper = operator.peek();
                if (oper == null){
                    try {
                        operator.push(String.valueOf(getResult(str)));
                    } catch (Exception e) {
                        logger.error("{} calculate failed!", str);
                    }
                } else {
                    if (!PATTERN_INNER.matcher(oper).find()) {
                        try {
                            operator.push(String.valueOf(getResult(str)));
                        } catch (Exception e) {
                            logger.error("{} calculate failed!", str);
                        }
                    } else {
                        str = CharSequenceUtil.concat(true, operator.pop(), str);
                        str = replaceBack(str);
                        operator.push(String.valueOf(operatorCal(oper, str, stepResultMap)));
                    }
                }
            }  else {
                operator.push(token);
            }
        }

        String finalFormula = "";
        while (!operator.isEmpty()) {
            String str = operator.pop();
            finalFormula = CharSequenceUtil.concat(true, str, finalFormula);
        }
        if (finalFormula.contains("[]")){
            finalFormula = finalFormula.replace("[]", "0");
        }
        try {
            result = BigDecimal.valueOf(Double.parseDouble(Compute.getResult(finalFormula)));
        } catch (Exception e) {
            logger.error("calculate failed!{}", finalFormula, e);
        }
        return result;
    }

    private static String replaceBack(String expr) {
        return expr.replace("/", OperationEnum.DIVIDE.getValue() )
            .replace("*", OperationEnum.MULTIPLY.getValue())
            .replace("/100","%" )
            .replace("M",OperationEnum.MAX.getValue())
            .replace("L", OperationEnum.MIN.getValue())
            .replace("C",OperationEnum.COUNT.getValue())
            .replace("S", OperationEnum.IFS.getValue())
            .replace("U", OperationEnum.UP.getValue())
            .replace("@", OperationEnum.IN.getValue())
            .replace("&", OperationEnum.AND.getValue())
            .replace("#", OperationEnum.OR.getValue());
    }

    private static BigDecimal operatorCal(String operator, String formula, Map<String, Object> stepResultMap) {
        return OperatorFactory.getOperatorByType(keyMap.get(operator)).process(formula, stepResultMap);
    }

    private static String replaceExpr(String expr) {
        return expr.replace(OperationEnum.DIVIDE.getValue(), "/")
            .replace(OperationEnum.MULTIPLY.getValue(), "*")
            .replace("%", "/100")
            .replace(OperationEnum.MAX.getValue(), "M")
            .replace(OperationEnum.MIN.getValue(), "L")
            .replace(OperationEnum.COUNT.getValue(), "C")
            .replace(OperationEnum.IFS.getValue(), "S")
            .replace(OperationEnum.UP.getValue(), "U")
            .replace(OperationEnum.IN.getValue(), "@")
            .replace(OperationEnum.AND.getValue(), "&")
            .replace(OperationEnum.OR.getValue(), "#");
    }

    public static void main(String[] args) {
        String s = "ADDD你好";
        System.out.println(calculate("(2.0×4)÷([5]×[5])×3", null));
        System.out.println(containsLetterOrChinese(s));;
    }
}
