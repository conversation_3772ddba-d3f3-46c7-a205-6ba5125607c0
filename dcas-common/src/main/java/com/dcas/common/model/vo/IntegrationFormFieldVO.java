package com.dcas.common.model.vo;

import com.dcas.common.model.other.OptionSelect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/6/11 16:42
 * @since 1.0.0
 */
@Data
public class IntegrationFormFieldVO {

    @ApiModelProperty(value = "字段定位")
    private String location;

    @ApiModelProperty(value = "所属层级")
    private Integer level;

    @ApiModelProperty(value = "字段名称")
    private String label;

    @ApiModelProperty(value = "输入框类型")
    private String type;

    @ApiModelProperty(value = "字段名称")
    private String name;

    @ApiModelProperty(value = "字段类型")
    private String columnType;

    @ApiModelProperty(value = "字段值，如果入参来源是接口结果，这里保存的是上个接口的location")
    private String value;

    @ApiModelProperty(value = "是否必填")
    private Boolean required;

    @ApiModelProperty(value = "参数类型 0-header； 1-入参；2-出参")
    private Integer paramType;

    // 出参专属
    @ApiModelProperty(value = "展示字段")
    private String displayName;

    @ApiModelProperty(value = "参数要求：1-结果展示；2-筛选条件")
    private Integer dataRequired;

    @ApiModelProperty("数据来源:1-终端输入; 2-接口结果")
    private Integer dataType;

    // 下拉框参数
    @ApiModelProperty("是否显示箭头")
    private Boolean showArrow;

    @ApiModelProperty("是否显示搜索框")
    private Boolean showSearch;

    @ApiModelProperty("下拉框模式：multiple-多选；single-单选")
    private String mode;

    @ApiModelProperty("下拉框选项")
    private List<OptionSelect<String>> options;
}
