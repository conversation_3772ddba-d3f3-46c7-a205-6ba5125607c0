package com.dcas.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.mapper.ApiInterfaceMapper;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.system.service.IApiInterfaceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * API接口服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiInterfaceServiceImpl extends ServiceImpl<ApiInterfaceMapper, ApiInterface> implements IApiInterfaceService {
    
    private final ApiInterfaceMapper apiInterfaceMapper;
    
    @Override
    public List<ApiInterface> getByReleaseId(Long releaseId, Integer taskType) {
        return apiInterfaceMapper.selectByReleaseIdAndTaskType(releaseId, taskType);
    }
}
