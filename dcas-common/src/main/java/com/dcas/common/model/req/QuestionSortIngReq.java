package com.dcas.common.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/4/14 11:26
 * @since 1.3.0
 */
@Data
public class QuestionSortIngReq {

    @NotNull(message = "模板id不能为空")
    @ApiModelProperty("模板id")
    private Long templateId;

    @NotNull(message = "题干排序列表不能为空")
    @ApiModelProperty("题干排序列表")
    private List<QuestionSort> questions;

    @Data
    public static class QuestionSort {
        @NotEmpty(message = "问题id不能为空")
        @ApiModelProperty("问题id")
        private String questionId;

        @NotNull(message = "序号不能为空")
        @ApiModelProperty("序号")
        private Integer sort;
    }
}
