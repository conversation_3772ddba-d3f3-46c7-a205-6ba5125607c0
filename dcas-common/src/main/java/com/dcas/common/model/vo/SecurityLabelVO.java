package com.dcas.common.model.vo;

import com.dcas.common.domain.entity.CoFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/3 11:35
 * @since 1.4.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecurityLabelVO {

    @ApiModelProperty(value = "检查模块")
    private String model;

    @ApiModelProperty(value = "检查类别列表")
    private List<Category> categories;

    @Data
    @Builder
    public static class Category {
        @ApiModelProperty(value = "检查类别名称")
        private String categoryName;
        @ApiModelProperty(value = "检查类别排序")
        private Integer sort;
        @ApiModelProperty(value = "是否已完成")
        private Boolean finished;
        @ApiModelProperty(value = "是否产品对接能力")
        private Boolean productAbility;
    }

}
