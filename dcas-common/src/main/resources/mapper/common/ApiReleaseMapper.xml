<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.ApiReleaseMapper">
    <!-- 查询产品能力树形结构 -->
    <select id="selectApiReleaseList" resultType="com.dcas.common.domain.entity.ApiRelease">
        SELECT a.id,
               t.name as capabilityName,
               a.logo,
               a.capability,
               t.type_id as tagTypeId,
               a.name,
               a.version
        FROM api_release a inner join tag t on t.id::varchar = a.capability where a.enable = true
        ORDER BY a.capability
    </select>
</mapper>