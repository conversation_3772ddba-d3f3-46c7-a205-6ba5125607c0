package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.domain.entity.ApiIntegratedProduct;
import com.dcas.common.domain.entity.ApiRelease;
import com.dcas.common.domain.entity.Tag;
import com.dcas.common.enums.IntegrationStatusEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.ApiIntegratedProductMapper;
import com.dcas.common.mapper.ApiInterfaceMapper;
import com.dcas.common.mapper.TagMapper;
import com.dcas.common.model.dto.StepMsgDTO;
import com.dcas.common.model.req.CreateIntegrationReq;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.UpdateIntegrationReq;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.ProductInfoVO;
import com.dcas.common.model.vo.ProductIntegrationVO;
import com.dcas.common.utils.Func;
import com.dcas.system.service.ApiIntegratedProductService;
import com.dcas.system.service.IApiCallService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集成产品服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiIntegratedProductServiceImpl extends ServiceImpl<ApiIntegratedProductMapper, ApiIntegratedProduct> implements ApiIntegratedProductService {

    private final TagMapper tagMapper;
    private final IApiCallService apiCallService;
    private final ApiReleaseServiceImpl apiReleaseService;
    private final ApiInterfaceMapper apiInterfaceMapper;

    @Override
    @SchemaSwitch
    public List<ProductIntegrationVO> getIntegrationPage(String capability) {
        QueryWrapper<ApiIntegratedProduct> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(capability)) {
            queryWrapper.eq("capability", capability);
        }
        List<ApiIntegratedProduct> apiIntegratedProducts = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(apiIntegratedProducts))
            return CollUtil.newArrayList();
        Map<Long, String> tagMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("status", 0))
                .stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        Map<Long, ApiRelease> releaseMap = apiReleaseService.getBaseMapper().selectApiReleaseList().stream().collect(Collectors.toMap(ApiRelease::getId, Function.identity()));
        return apiIntegratedProducts.stream().collect(Collectors.groupingBy(ApiIntegratedProduct::getCapability)).entrySet().stream().map(e -> {
            ProductIntegrationVO vo = new ProductIntegrationVO();
            vo.setCapability(e.getKey());
            vo.setCapabilityName(tagMap.get(Long.parseLong(e.getKey())));
            vo.setProducts(e.getValue().stream().map(apiIntegratedProduct -> {
                ProductInfoVO productInfoVO = new ProductInfoVO();
                productInfoVO.setId(apiIntegratedProduct.getId());
                productInfoVO.setReleaseId(apiIntegratedProduct.getReleaseId());
                productInfoVO.setCapability(apiIntegratedProduct.getCapability());
                productInfoVO.setCapabilityName(tagMap.get(Long.parseLong(apiIntegratedProduct.getCapability())));
                productInfoVO.setName(apiIntegratedProduct.getName());
                productInfoVO.setUrl(apiIntegratedProduct.getUrl());
                productInfoVO.setProductName(apiIntegratedProduct.getProductName());
                productInfoVO.setCompany(apiIntegratedProduct.getCompany());
                productInfoVO.setCompanyName(tagMap.get(Long.parseLong(apiIntegratedProduct.getCompany())));
                productInfoVO.setVersion(apiIntegratedProduct.getVersion());
                productInfoVO.setApiType(apiInterfaceMapper.selectApiTypeByReleaseId(apiIntegratedProduct.getReleaseId()));
                ApiRelease apiRelease = releaseMap.get(apiIntegratedProduct.getReleaseId());
                productInfoVO.setLogo(Objects.nonNull(apiRelease) ? apiRelease.getLogo() : apiIntegratedProduct.getLogo());
                String status = apiIntegratedProduct.getStatus();
                if (Objects.isNull(apiRelease)) {
                    status = IntegrationStatusEnum.DELETED_FROM_KNOWLEDGE_BASE.getCode();
                } else if (apiRelease.getVersion() > productInfoVO.getVersion()) {
                    status = IntegrationStatusEnum.PARAMETER_UPDATED.getCode();
                }
                productInfoVO.setStatus(status);
                productInfoVO.setCreateTime(apiIntegratedProduct.getCreateTime());
                productInfoVO.setCreateBy(apiIntegratedProduct.getCreateBy());
                productInfoVO.setUpdateTime(apiIntegratedProduct.getUpdateTime());
                productInfoVO.setUpdateBy(apiIntegratedProduct.getUpdateBy());
                productInfoVO.setParams(JSONUtil.toList(apiIntegratedProduct.getConfigParams(), IntegrationFormFieldVO.class));
                baseMapper.updateStatusById(productInfoVO.getId(), productInfoVO.getStatus(), productInfoVO.getLogo());
                return productInfoVO;
            }).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList());
    }


    @Override
    @SchemaSwitch
    @Transactional(rollbackFor = Exception.class)
    public void createIntegration(CreateIntegrationReq request) {
        // 检查集成名称是否重复
        LambdaQueryWrapper<ApiIntegratedProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiIntegratedProduct::getCapability, request.getCapability());
        wrapper.eq(ApiIntegratedProduct::getProductName, request.getProductName());
        ApiIntegratedProduct existing = this.getOne(wrapper);
        if (existing != null) {
            throw new ServiceException("当前产品已对接");
        }

        ApiRelease apiRelease = apiReleaseService.getBaseMapper().selectById(request.getReleaseId());

        // 创建集成产品记录
        ApiIntegratedProduct apiIntegratedProduct = new ApiIntegratedProduct();
        apiIntegratedProduct.setReleaseId(request.getReleaseId());
        apiIntegratedProduct.setCapability(request.getCapability());
        apiIntegratedProduct.setName(request.getName());
        apiIntegratedProduct.setUrl(request.getUrl());
        apiIntegratedProduct.setProductName(request.getProductName());
        apiIntegratedProduct.setCompany(apiRelease.getCompany());
        apiIntegratedProduct.setLogo(apiRelease.getLogo());
        apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
        apiIntegratedProduct.setVersion(apiRelease.getVersion());
        Func.beforeInsert(apiIntegratedProduct);

        // 转换配置参数为JSON字符串
        if (CollUtil.isNotEmpty(request.getConfigParams())) {
            apiIntegratedProduct.setConfigParams(JSONUtil.toJsonStr(request.getConfigParams()));
        }

        // 测试连接
        List<ApiInterface> apiInterfaces = apiInterfaceMapper.selectByReleaseId(apiIntegratedProduct.getReleaseId());
        if (CollUtil.isNotEmpty(apiInterfaces)) {
            // 根据排序选择第一个接口
            ApiInterface apiInterface = apiInterfaces.get(0);
            // 使用鉴权参数验证连接
            StepMsgDTO stepMsgDTO = apiCallService.testApiConnection(apiInterface, request.getConfigParams(), apiIntegratedProduct.getUrl());
            if (stepMsgDTO.getSuccess()) {
                apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
            } else {
                throw new ServiceException(String.format("连接测试失败: %s", stepMsgDTO.getMessage()));
            }
        } else {
            throw new ServiceException("未找到接口信息");
        }

        this.save(apiIntegratedProduct);
    }

    @Override
    @SchemaSwitch
    @Transactional(rollbackFor = Exception.class)
    public void updateIntegration(UpdateIntegrationReq request) {
        ApiIntegratedProduct apiIntegratedProduct = this.getById(request.getId());
        if (apiIntegratedProduct == null) {
            throw new RuntimeException("集成产品不存在");
        }

        ApiRelease apiRelease = apiReleaseService.getBaseMapper().selectById(request.getReleaseId());

        apiIntegratedProduct.setReleaseId(request.getReleaseId());
        apiIntegratedProduct.setCapability(request.getCapability());
        apiIntegratedProduct.setName(request.getName());
        apiIntegratedProduct.setUrl(request.getUrl());
        apiIntegratedProduct.setProductName(request.getProductName());
        apiIntegratedProduct.setCompany(apiRelease.getCompany());
        apiIntegratedProduct.setLogo(apiRelease.getLogo());
        apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
        apiIntegratedProduct.setVersion(apiRelease.getVersion());

        // 更新集成产品信息
        Func.beforeUpdate(apiIntegratedProduct);

        // 转换配置参数为JSON字符串
        if (StrUtil.isNotEmpty(apiIntegratedProduct.getConfigParams())) {
            apiIntegratedProduct.setConfigParams(JSONUtil.toJsonStr(request.getConfigParams()));
        }

        // 测试连接
        List<ApiInterface> apiInterfaces = apiInterfaceMapper.selectByReleaseId(apiIntegratedProduct.getReleaseId());
        if (CollUtil.isNotEmpty(apiInterfaces)) {
            // 根据排序选择第一个接口
            ApiInterface apiInterface = apiInterfaces.get(0);
            StepMsgDTO stepMsgDTO = apiCallService.testApiConnection(apiInterface, request.getConfigParams(), apiIntegratedProduct.getUrl());
            if (stepMsgDTO.getSuccess()) {
                apiIntegratedProduct.setStatus(IntegrationStatusEnum.NORMAL_CONNECTION.getCode());
            } else {
                throw new ServiceException(String.format("连接测试失败: %s", stepMsgDTO.getMessage()));
            }
        } else {
            throw new ServiceException("未找到接口信息");
        }

        updateById(apiIntegratedProduct);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIntegrations(IdsReq ids) {
        removeByIds(ids.getIds());
    }

    @Override
    @SchemaSwitch
    public ProductInfoVO getProductInfo(Long id) {
        ApiIntegratedProduct apiIntegratedProduct = this.getById(id);
        if (apiIntegratedProduct == null) {
            throw new ServiceException("对接产品不存在");
        }

        Map<Long, String> tagMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("status", 0))
                .stream().collect(Collectors.toMap(Tag::getId, Tag::getName));

        ProductInfoVO productInfoVO = new ProductInfoVO();
        productInfoVO.setId(apiIntegratedProduct.getId());
        productInfoVO.setReleaseId(apiIntegratedProduct.getReleaseId());
        productInfoVO.setCapability(apiIntegratedProduct.getCapability());
        productInfoVO.setCapabilityName(tagMap.get(Long.parseLong(apiIntegratedProduct.getCapability())));
        productInfoVO.setName(apiIntegratedProduct.getName());
        productInfoVO.setUrl(apiIntegratedProduct.getUrl());
        productInfoVO.setProductName(apiIntegratedProduct.getProductName());
        productInfoVO.setCompany(apiIntegratedProduct.getCompany());
        productInfoVO.setCompanyName(tagMap.get(Long.parseLong(apiIntegratedProduct.getCompany())));
        productInfoVO.setLogo(apiIntegratedProduct.getLogo());
        productInfoVO.setStatus(apiIntegratedProduct.getStatus());
        productInfoVO.setCreateTime(apiIntegratedProduct.getCreateTime());
        productInfoVO.setCreateBy(apiIntegratedProduct.getCreateBy());
        productInfoVO.setUpdateTime(apiIntegratedProduct.getUpdateTime());
        productInfoVO.setUpdateBy(apiIntegratedProduct.getUpdateBy());
        productInfoVO.setVersion(apiIntegratedProduct.getVersion());

        // 状态为参数更新需要获取新的参数展示
        if (StrUtil.equals(apiIntegratedProduct.getStatus(), IntegrationStatusEnum.PARAMETER_UPDATED.getCode())) {
            List<IntegrationFormFieldVO> params = apiReleaseService.getProductHeader(apiIntegratedProduct.getReleaseId());
            productInfoVO.setParams(params);
        } else {
            JSONArray jsonArray = JSONUtil.parseArray(apiIntegratedProduct.getConfigParams());
            List<IntegrationFormFieldVO> params = jsonArray.toList(IntegrationFormFieldVO.class);
            productInfoVO.setParams(params);
        }
        return productInfoVO;
    }
}
