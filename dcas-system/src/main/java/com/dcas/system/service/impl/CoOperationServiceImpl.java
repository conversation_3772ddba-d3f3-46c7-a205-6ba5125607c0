package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.annotation.DataScope;
import com.dcas.common.annotation.DataValidator;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.TreeSelect;
import com.dcas.common.enums.*;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.exception.params.FailParamsException;
import com.dcas.common.utils.*;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.utils.sign.Base64;
import com.dcas.common.model.dto.*;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.param.ModelParam;
import com.dcas.common.model.vo.*;
import com.dcas.discovery.service.SourceConfigService;
import com.dcas.system.handler.OperationAbilityHandler;
import com.dcas.system.holder.RegionCodeCacheHolder;
import com.dcas.system.holder.SafetyImpactsClassificationCacheHolder;
import com.dcas.system.manager.DynamicSchemaManager;
import com.dcas.common.mapper.*;
import com.dcas.system.service.*;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 作业管理服务层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoOperationServiceImpl implements CoOperationService {

    /**
     * 业务逻辑：
     * 1.作业新建之后，状态默认是0=待启动; 点击执行变成1=执行中;点击复核变成2=复核中；点击结项变成3=已结项
     * 2.执行中计算执行进度，进度100%就是执行完成；复核中同理；点击结项进度默认100%
     * 3.创建评估作业页面，要更新客户资料表中：根据客户id更新涉及行业、涉及地区、是否设计境外、境外业务地区
     * 4.创建评估作业页面，是否标准服务包：标准服务包括：所有分类树子节点
     * 5.进度100%和评估状态=评估中=待复核，这时才能点复核
     */

    private final TagMapper tagMapper;
    private final CoFileServiceImpl coFileService;
    private final CoProjectMapper coProjectMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoProcessTreeMapper coProcessTreeMapper;
    private final CoProgressMapper coProgressMapper;
    private final CoIndustryTypeMapper coIndustryTypeMapper;
    private final CoInventoryService coInventoryService;
    private final CoVerificationService coVerificationService;
    private final CoGapAnalysisService coGapAnalysisService;
    private final CoViewCustomerService coViewCustomerService;
    private final ISysUpgradeService sysUpgradeService;
    private final CoBasicEnService coBasicEnService;
    private final CoLegalService coLegalService;
    private final CoLifecycleService coLifecycleService;
    private final CoAuthorityService coAuthorityService;
    private final CoAssetViewServiceImpl coAssetViewService;
    private final CoAssetUserServiceImpl coAssetUserService;
    private final TemplateMapper templateMapper;
    private final QuestionnaireMapper questionnaireMapper;
    private final QuestionnaireContentServiceImpl questionnaireContentService;
    private final DiscoveryClassifyDataMapper discoveryClassifyDataMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final DetectionResultServiceImpl detectionResultService;
    private final DynamicProcessTreeServiceImpl dynamicProcessTreeService;
    private final TemplateCombinationServiceImpl templateCombinationService;
    private final AdviseItemService adviseItemService;
    private final AdviseSchemeService adviseSchemeService;
    private final OperationModelMapper operationModelMapper;
    private final ModelConfigMapper modelConfigMapper;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final LibrarySyncConfigMapper librarySyncConfigMapper;
    private final ISysConfigService sysConfigService;
    private final DetermineAssetDataService determineAssetDataService;
    private final DetermineAssetTypeService determineAssetTypeService;

    private final OperationAbilityHandler operationAbilityHandler;
    private final SpecialEvaluationConfigMapper specialEvaluationConfigMapper;
    private final SpecCalcResultMapper specCalcResultMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final SourceConfigMapper sourceConfigMapper;
    private final DiscoveryPlanMapper discoveryPlanMapper;
    private final DiscoverySourceMapper discoverySourceMapper;
    private final DiscoveryTemplateContentMapper discoveryTemplateContentMapper;
    private final IMidInventoryResultService iMidInventoryResultService;
    private final IMidOperationService iMidOperationService;
    private final IMidVulResultService iMidVulResultService;
    private final ThreatLevelMapper threatLevelMapper;
    private final CoConstantMapper coConstantMapper;
    private final SafetyImpactsClassificationCacheHolder safetyImpactsClassificationCacheHolder;
    private final StandardItemMapper standardItemMapper;
    private final ThreatTemplateMapper threatTemplateMapper;
    private final SourceConfigService sourceConfigService;
    private final QuestionnaireItemDegreeService questionnaireItemDegreeService;
    private final ICoSystemResultService iCoSystemResultService;
    private final IApiReleaseService iApiReleaseService;


    public static final String IMAGE_PNG_BASE64 = "data:image/png;base64,";
    public static final String COMMON_REGION_TAG = "全国";
    public static final String COMMON_INDUSTRY_TAG = "全行业";

    /**
     * 新增作业
     *
     * @param requestModel request
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public String add(RequestModel<OperationAddDTO> requestModel, String version) {
        OperationAddDTO dto = requestModel.getPrivator();
        CheckUtil.checkParams(dto);
        //判断项目id是否存在
        CoProject coProject = coProjectMapper.selectById(dto.getProjectId());
        if (ObjectUtils.isEmpty(coProject)) {
            throw new FailParamsException("项目id不存在");
        }
        //作业名称重复校验
        QueryWrapper<CoOperation> query = new QueryWrapper<>();
        query.eq("operation_name", dto.getOperationName());
        query.eq("del_flag", '0');
        int count = coOperationMapper.selectCount(query);
        if(count > 0) {
            throw new FailParamsException("作业名称已存在");
        }

        CoOperation entity = new CoOperation();
        BeanUtils.copyProperties(dto, entity);
        // 创建用户
        entity.setCreateBy(SecurityUtils.getNickname());
        entity.setCreateTime(DateUtil.date());
        entity.setUpdateTime(DateUtil.date());
        entity.setUserId(SecurityUtils.getUserId());
        entity.setDeptId(SecurityUtils.getDeptId());
        if (StrUtil.isEmpty(version))
            version = sysUpgradeService.needSync().getVersion();
        entity.setVersion(version);
        //主键
        String id = SnowFlake.getId();
        entity.setOperationId(id);
        // 生成动态标签
        List<DynamicProcessTree> dynamicProcessTrees = generateDynamicLabel(dto, id);

        //组装服务内容
        StringBuilder builder = new StringBuilder();
        builder.append(dto.getServiceContent());
        List<String> processList = StrUtil.split(dto.getServiceContent(), StrUtil.COMMA);
        if (processList.contains(LabelEnum.ZZDY.getCode().toString()) && CollUtil.isNotEmpty(dto.getBusSystemTags())) {
            processList.add(LabelEnum.XTDY.getCode().toString());
        }
        processList.forEach(p->{
            List<DynamicProcessTree> coProcessTree = dynamicProcessTrees.stream().filter(t -> Objects.equals(t.getSrcTreeId(), Long.valueOf(p))).collect(Collectors.toList());
            coProcessTree.forEach(t -> {
                builder.append(",").append(t.getTreeId()).append(",").append(t.getAncestors());
            });
        });
        List<String> process = Arrays.asList(builder.toString().split(","));
        entity.setServiceContent(StringUtils.join(process.stream().filter(p -> StringUtils.isNotBlank(p) && !"107".equals(p) && !"0".equals(p)).distinct().toArray(), ","));
        // 判断是否选择现状核验模块
        entity.setModelType(entity.getServiceContent().contains(LabelEnum.XZHY.getCode().toString()) ? 1 : 0);
        //保存
        coOperationMapper.insert(entity);
        // 更新组合模板绑定作业id
        TemplateCombination templateCombination = new TemplateCombination();
        templateCombination.setId(dto.getTemplateId());
        templateCombination.setOperationId(entity.getOperationId());
        templateCombinationService.updateById(templateCombination);
        String operationId = entity.getOperationId();
        DynamicSchemaManager.addVersion(version, operationId);

        // 保存中间表-作业信息表
        Map<String, Long> systemIdMap = dynamicProcessTrees.stream().filter(dynamicProcessTree -> dynamicProcessTree.getSrcTreeId() == 107).collect(
            Collectors.toMap(DynamicProcessTree::getTreeName, DynamicProcessTree::getTreeId));

        // 若业务系统标识为空 则默认一般系统
        if(CollUtil.isEmpty(dto.getBusSystemTags())){
            String[] busSystemArr = dto.getRelatedSystem().split(StrPool.COMMA);
            dto.setBusSystemTags(
                Arrays.stream(busSystemArr).map(busSystem -> new BusSystemTagDTO(busSystem, SystemTagEnum.GENERAL_SYSTEM.getTag())).collect(
                Collectors.toList()));
        }
        List<MidOperation> midOperationList = buildMidOperationList(entity, dto.getBusSystemTags(), systemIdMap);
        iMidOperationService.saveBatch(midOperationList);
        return operationId;
    }

    private List<MidOperation> buildMidOperationList(CoOperation entity, List<BusSystemTagDTO> busSystemTags,
        Map<String, Long> systemIdMap) {
        List<MidOperation> midOperationList = new ArrayList<>();
        TemplateCombination templateCombination = templateCombinationService.getById(entity.getTemplateId());

        for (BusSystemTagDTO busSystemTag : busSystemTags) {
            MidOperation midOperation = new MidOperation();
            midOperation.setOperationId(entity.getOperationId());
            midOperation.setSystemId(systemIdMap.get(busSystemTag.getBusSystem()));
            midOperation.setBusSystem(busSystemTag.getBusSystem());
            midOperation.setTemplateIds(templateCombination != null ? templateCombination.getTemplateIds() : new Long[] {
                entity.getTemplateId()});
            midOperation.setBusSystemTag(busSystemTag.getTag());
            midOperation.setProjectId(entity.getProjectId());
            midOperationList.add(midOperation);
        }
        return midOperationList;
    }

    private List<DynamicProcessTree> generateDynamicLabel(OperationAddDTO dto, String id) {
        List<DynamicProcessTree> res = new ArrayList<>();
        // 生成动态标签
        QueryWrapper<CoProcessTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", '0');
        List<CoProcessTree> coProcessTrees = coProcessTreeMapper.selectList(queryWrapper);
        AtomicInteger index = new AtomicInteger(0);
        coProcessTrees.forEach(t -> {
            if (Objects.equals(t.getTreeId(), LabelEnum.XTDY.getCode())) {
                List<String> system = StrUtil.split(dto.getRelatedSystem(), StrUtil.COMMA);
                for (String s : system) {
                    int i = index.incrementAndGet();
                    res.add(DynamicProcessTree.builder()
                            .treeId(Long.parseLong(t.getTreeId().toString() + i))
                            .treeName(s)
                            .ancestors(t.getAncestors())
                            .parentId(t.getParentId())
                            .orderNum(t.getOrderNum() + i)
                            .operationId(id)
                            .srcTreeId(t.getTreeId())
                            .build());
                }
            } else {
                res.add(DynamicProcessTree.builder()
                        .treeId(t.getTreeId())
                        .treeName(t.getTreeName())
                        .ancestors(t.getAncestors())
                        .parentId(t.getParentId())
                        .orderNum(t.getTreeId() == 108L ? index.incrementAndGet() + t.getOrderNum() : t.getOrderNum())
                        .operationId(id)
                        .srcTreeId(t.getTreeId())
                        .build());
            }
        });
        List<Long> labelIds = new ArrayList<>();
        List<Long> serviceIds = StrUtil.split(dto.getServiceContent(), StrUtil.COMMA).stream().mapToLong(Long::valueOf).boxed().collect(Collectors.toList());
        List<Long> parentIds = Arrays.stream(LabelEnum.values()).filter(l -> serviceIds.contains(l.getCode())).map(LabelEnum::getPCode).collect(Collectors.toList());

        // 判断是否存在技术能力检测，脱敏加密能力+对接产品能力
        Set<Long> capabilityIdSet = iApiReleaseService.getProductCapability().stream()
            .map(abilityModelVO -> abilityModelVO.getCode().longValue()).collect(Collectors.toSet());
        if (serviceIds.contains(AbilityType.ENCRYPT.getCode().longValue())
            || serviceIds.contains(AbilityType.DESENSITIZATION.getCode().longValue())
            || CollUtil.containsAny(serviceIds, capabilityIdSet)){
            labelIds.add(118L);
        }
        labelIds.addAll(serviceIds);
        labelIds.addAll(parentIds);
        labelIds.add(100L);
        // 系统标签即使在没有选择调研问卷，否则查询不出业务系统
        labelIds.add(LabelEnum.XTDY.getCode());
        List<DynamicProcessTree> collect = res.stream().filter(r -> labelIds.contains(r.getSrcTreeId())).collect(Collectors.toList());
        dynamicProcessTreeService.saveBatch(collect);
        return res;
    }

    /**
     * 批量删除作业记录
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:39
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int remove(RequestModel<PrimaryKeyListDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        List<String> operationIds = dto.getPrivator().getIdList();
        QueryWrapper<DynamicProcessTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("operation_id", operationIds);
        dynamicProcessTreeService.remove(queryWrapper);
        preSourceConfigMapper.delete(new QueryWrapper<PreSourceConfig>().in("operation_id", operationIds));
        List<SourceConfig> sourceConfigs = sourceConfigMapper.selectList(new QueryWrapper<SourceConfig>().in("operation_id", operationIds));
        sourceConfigMapper.delete(new QueryWrapper<SourceConfig>().in("operation_id", operationIds));
        List<Integer> configIds = sourceConfigs.stream().map(SourceConfig::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(configIds)) {
            List<DiscoverySource> discoverySources = discoverySourceMapper.selectList(new QueryWrapper<DiscoverySource>().in("source_id", configIds));
            discoverySourceMapper.delete(new QueryWrapper<DiscoverySource>().in("source_id", configIds));
            List<Long> planIds = discoverySources.stream().map(DiscoverySource::getPlanId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(planIds))
                discoveryPlanMapper.delete(new QueryWrapper<DiscoveryPlan>().in("id", planIds));
        }
        // 删除组合模板
        templateCombinationService.remove(new QueryWrapper<TemplateCombination>().in("operation_id", operationIds));
        return coOperationMapper.updateOperationByIds(operationIds);
    }


    /**
     * 更新项目管理记录
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/7/21 17:31
     */
    @Override
    public int edit(RequestModel<UpdateOperationDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        CoOperation operation = coOperationMapper.selectById(dto.getPrivator().getOperationId());

        CoOperation coOperation = new CoOperation();
        coOperation.setOperationId(dto.getPrivator().getOperationId());
        coOperation.setUpdateTime(DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", DateUtils.getTime()));

        /*
         *  已复核不能点评估；已结项不能点评估、复核；状态是null,进度是0可以点评估;状态是评估中，进度是100%才能点复核；状态是复核中，进度是100%才能点结项
         */
        //操作类型是执行
        if (dto.getPrivator().getOperationType().compareTo(OperationTypeEnum.EXECUTOR.getCode()) == 0) {
            if (ObjectUtil.isEmpty(operation.getStatus()) || operation.getStatus() == 0) {
                //操作类型是执行并且状态是空则更新状态=1，进度=0
                coOperation.setStatus(NumEnum.ONE.getCode());
                coOperation.setProgress(BigDecimal.ZERO);
            } else if (operation.getStatus() == 1 && operation.getProgress().doubleValue() < 100) {
                //操作类型是执行并且状态是1则更新操作时间
                coOperation.setUpdateTime(DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", DateUtils.getTime()));
            } else {
                throw new FailParamsException("未满足执行条件");
            }
        } else if (dto.getPrivator().getOperationType().compareTo(OperationTypeEnum.REVIEWER.getCode()) == 0) {
            if (operation.getStatus() == 1 && operation.getProgress().doubleValue() == 100) {
                //操作类型是复核并且状态是1 && 进度是100 则更新状态=2，进度=0；清空进度表重新计算
                coOperation.setStatus(2);
                coOperation.setProgress(BigDecimal.ZERO);
                //清空进度表
                QueryWrapper<CoProgress> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("operation_id",dto.getPrivator().getOperationId());
                coProgressMapper.delete(queryWrapper);
            } else {
                throw new FailParamsException("未满足复核条件");
            }
        } else if (dto.getPrivator().getOperationType().compareTo(OperationTypeEnum.END.getCode()) == 0) {
            if (operation.getStatus() == 2 && operation.getProgress().doubleValue() == 100) {
                // 结果校验
                operationAbilityHandler.abilityChainVerify(operation.getOperationId());
                //操作类型是结项并且状态是2 && 进度是100 则更新状态=3
                coOperation.setStatus(3);
            } else {
                throw new FailParamsException("未满足结项条件");
            }
        }
        return coOperationMapper.updateById(coOperation);
    }

    /**
     * 查询作业主表
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:27
     */
    @Override
    @DataScope(deptAlias = "a", userAlias = "a", extra = "job")
    public PageInfo<QueryOperationVo> query(RequestModel<QueryOperationDTO> dto) {

        Map<String, Boolean> libraryVersionMap = librarySyncConfigMapper.selectList(new QueryWrapper<>())
                .stream().collect(Collectors.toMap(LibrarySyncConfig::getVersion, LibrarySyncConfig::getIsLatest));
        //分页
        try(Page<Object> ignored = PageHelper.startPage(dto.getPageNum(), dto.getPageSize())) {
            //查询结果
            List<QueryOperationVo> list = coOperationMapper.queryOperationList(dto.getPrivator());
            list.forEach(l -> {
                if (CollUtil.isEmpty(libraryVersionMap)) {
                    l.setIsCopy(Boolean.TRUE);
                } else {
                    l.setIsCopy(libraryVersionMap.getOrDefault(l.getVersion(), Boolean.FALSE));
                }
            });
            return new PageInfo<>(list);
        }
    }


    /**
     * 更新进度
     *
     * @param dto request
     * @Date 2022/8/11 14:21
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void editProgress(RequestModel<CommonDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        // 资产发现作业校验
        if (Objects.equals(dto.getPrivator().getLabelId(), LabelEnum.SMZQ.getCode())) {
            checkDiscoveryTaskStatus(dto.getPrivator());
        }

        //计算已完成进度，存入进度表，一个模块调用一次进度记为1，同个模块调用多次进度也是1
        QueryWrapper<CoProgress> query = new QueryWrapper<>();
        query.eq("operation_id", dto.getPrivator().getOperationId());
        query.eq("label_id", dto.getPrivator().getLabelId());
        CoProgress coProgresses = coProgressMapper.selectOne(query);
        if (Objects.isNull(coProgresses)) {
            CoProgress coProgress = new CoProgress();
            coProgress.setOperationId(dto.getPrivator().getOperationId());
            coProgress.setLabelId(dto.getPrivator().getLabelId());
            //这个字段暂时未用到
            coProgress.setOperationStatus(NumEnum.ZERO.getCode());
            coProgress.setUpdateTime(LocalDateTime.now());
            coProgressMapper.insert(coProgress);
        }

        //更新过程进度
        updateProcess(dto.getPrivator().getOperationId());
    }

    private void checkDiscoveryTaskStatus(CommonDto dto) {
        // 资产任务
        if (sourceConfigService.countUnFinishedTask(dto.getOperationId()) > 0){
            throw new ServiceException("存在未完成的敏感资产识别任务，无法提交!");
        }
        // 判断该作业所有业务系统是否都进行风险计算
        List<TreeLabelDTO> list = dynamicProcessTreeMapper.querySelectedBusSystemByOperationIdAndTreeId(dto.getOperationId(), LabelEnum.XTDY.getCode());
        List<String> allSystem = dynamicProcessTreeMapper.queryBusSystemNameByOperationIds(Collections.singletonList(dto.getOperationId()),LabelEnum.XTDY.getCode());
        if (list.size() != allSystem.size()){
            List<String> selectedSystemList = list.stream().map(TreeLabelDTO::getTreeName).collect(Collectors.toList());
            String busSystem = allSystem.stream().filter(s -> !selectedSystemList.contains(s)).collect(Collectors.joining("、"));
            throw new ServiceException(busSystem + "系统的风险评估尚未完成。请先添加威胁分析完成风险计算并重新尝试提交");
        }
    }

    @Override
    public void editVerProcess(String operationId) {
        QueryWrapper<CoProgress> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        List<CoProgress> coProgressList = coProgressMapper.selectList(queryWrapper);
        Optional<CoProgress> optional = coProgressList.stream().filter(l -> Objects.equals(LabelEnum.XZHY.getCode(), l.getLabelId())).findFirst();
        int count = questionnaireContentService.getBaseMapper().selectUnVerifyCount(operationId);
        if (optional.isPresent() && count > 0) {
            CoProgress coProgress = optional.get();
            double completedNum = Math.max(coProgressList.size() - 1, 0);
            double allNum = selectAllLabelNum(operationId);
            coProgressMapper.deleteById(coProgress.getProgressId());
            double progress = Arith.round(completedNum / allNum * 100, 2);
            UpdateWrapper<CoProgress> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("operation_id", operationId);
            updateWrapper.set("progress", BigDecimal.valueOf(progress));
            //更新进度表
            coProgressMapper.update(new CoProgress(), updateWrapper);
            //更新作业表
            CoOperation operation = new CoOperation();
            operation.setOperationId(operationId);
            operation.setProgress(BigDecimal.valueOf(progress));
            coOperationMapper.updateById(operation);
        }
    }

    private double selectAllLabelNum(String operationId) {
        //查询作业服务内容，获取子树数量
        CoOperation coOperation = coOperationMapper.selectById(operationId);
        List<String> serviceContentList = Arrays.asList(coOperation.getServiceContent().split(","));
        List<Integer> scList = serviceContentList.stream().filter(p-> !NumEnum.ONEHUNDRED.getInfo().equals(p) && !NumEnum.ONEHUNDREDONE.getInfo().equals(p) && !NumEnum.ONEHUNDREDTWO.getInfo().equals(p) && !NumEnum.ONEHUNDREDTHREE.getInfo().equals(p) && !NumEnum.ONEHUNDREDFOUR.getInfo().equals(p) && !NumEnum.ONEHUNDREDFIVE.getInfo().equals(p)).map(Integer::parseInt).collect(Collectors.toList());
        //全部模块数量
        return scList.size();
    }

    /**
     * 查询进度
     *
     * @param dto request
     * @return * @return String
     * @Date 2022/8/11 15:16
     */
    @Override
    public QueryProgressVo queryProgress(RequestModel<PrimaryKeyDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        //查询进度表
        QueryWrapper<CoProgress> query = new QueryWrapper<>();
        query.eq("operation_id", dto.getPrivator().getId());
        List<CoProgress> coProgresses = coProgressMapper.selectList(query);
        List<Long> labelIdList = coProgresses.stream().map(CoProgress::getLabelId).collect(Collectors.toList());
        QueryProgressVo vo = new QueryProgressVo();
        vo.setProgress(ObjectUtils.isEmpty(coProgresses) ? BigDecimal.ZERO : coProgresses.get(0).getProgress());
        vo.setLabelIdList(labelIdList);
        return vo;

    }

    /**
     * 查询涉及行业
     *
     * @param industryName request
     * @return * @return List<String>
     * @Date 2022/8/26 16:10
     */
    @Override
    public List<String> queryIndustry(String industryName) {

        List<String> industryNameList = new ArrayList<>();
        if (StringUtils.isNotBlank(industryName)) {
            QueryWrapper<CoIndustryType> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("industry_name", industryName);
            List<CoIndustryType> coIndustryTypes = coIndustryTypeMapper.selectList(queryWrapper);
            industryNameList = coIndustryTypes.stream().map(CoIndustryType::getIndustryName).collect(Collectors.toList());
        } else {
            List<CoIndustryType> coIndustryTypes = coIndustryTypeMapper.selectList(null);
            industryNameList = coIndustryTypes.stream().map(CoIndustryType::getIndustryName).collect(Collectors.toList());
        }
        return industryNameList;
    }

    /**
     * 获取评估作业下拉树列表
     *
     * @param dto request
     * @return * @return ResponseApi<List<TreeSelect>>
     * @Date 2022/10/18 11:32
     */
    @Override
    public List<TreeSelect> treeSelect(RequestModel<OperationIdDto> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());

        //查询作业中服务内容 只有除基础评估外的二级节点
        CoOperation coOperation = coOperationMapper.selectById(dto.getPrivator());
        Set<Long> labelIds = Arrays.stream(coOperation.getServiceContent().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toSet());
        if (coOperation.getModelType() == 2) {
            labelIds.add(108L);
        }
        List<TreeLabelDTO> treeLabelDTOS = dynamicProcessTreeMapper.selectByOperationId(dto.getPrivator().getOperationId(), labelIds);
        if (CollUtil.isEmpty(treeLabelDTOS)) {
            treeLabelDTOS = coProcessTreeMapper.selectByLabelIds(labelIds);
        }
        return Collections.singletonList(buildTree(treeLabelDTOS));
    }

    private TreeSelect buildTree(List<TreeLabelDTO> coProcessTrees) {
        // 使用HashMap来辅助构建树结构
        Map<Long, TreeSelect> map = new HashMap<>();
        for (TreeLabelDTO row : coProcessTrees) {
            Long id = row.getTreeId();
            String label = row.getTreeName();

            if (id != 107L && id.toString().startsWith("107")) {
                label = label + "系统调研";
            }

            TreeSelect node = new TreeSelect(id, label);
            map.put(id, node);
        }

        // 连接父子节点关系
        for (TreeLabelDTO row : coProcessTrees) {
            Long id = row.getTreeId();
            Long parentId = row.getParentId();
            if (parentId != null) {
                TreeSelect parent = map.get(parentId);
                TreeSelect node = map.get(id);
                if (parent != null && node != null) {
                    parent.addChild(node);
                }
            }
        }

        // 找到根节点
        TreeSelect root = null;
        for (TreeSelect node : map.values()) {
            if (node.getId() == 100) { // 假设根节点的id为0
                root = node;
                break;
            }
        }
        if (Objects.nonNull(root)) {
            // 删除空的一级节点
            root.getChildren().removeIf(next -> CollUtil.isEmpty(next.getChildren()));
        }

        return root;
    }

    @Override
    public List<LabelVO> qryTreeLabel() {
        return coProcessTreeMapper.qryTreeLabel();
    }

//    @DataValidator(type = TemplateTypeEnum.SURVEY_TEMPLATE)
    @Override
    @SchemaSwitch
    public LabelVO qryTemplate(TemplateQueryDTO dto) {
        List<LabelVO> list = new ArrayList<>();
        if (Objects.nonNull(dto.getSpecId())) {
            SpecialEvaluationConfig specialEvaluationConfig = specialEvaluationConfigMapper.selectById(dto.getSpecId());
            Template template = templateMapper.selectById(specialEvaluationConfig.getTemplateId());
            list.add(LabelVO.builder().id(template.getId()).name(template.getName()).understand(template.getUnderstand()).build());
        } else {
            final Map<String, String> industryMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", 2)
                    .eq("status", 0)).stream().collect(Collectors.toMap(t -> t.getId().toString(), Tag::getName));
            List<String> industryList = StrUtil.split(dto.getIndustry(), StrUtil.COMMA);
            // 这里的调研模板过滤掉专项中绑定的调研模板
            list = templateMapper.qryTemplate().stream().filter(t -> templateFilter(dto.getRegion(), industryList, t.getTag(), t.getRegionCode(), t.getIndustryCode(), industryMap)).map(t -> LabelVO.builder().id(t.getId()).name(t.getName()).understand(t.getUnderstand()).build()).collect(
                Collectors.toList());
        }
        // 转成Long[]数组
        Long[] templateIds = list.stream().map(LabelVO::getId).toArray(Long[]::new);
        String combinationName = list.stream().map(LabelVO::getName).collect(Collectors.joining(StrUtil.LF));
        TemplateCombination templateCombination = new TemplateCombination();
        templateCombination.setTemplateIds(templateIds);
        templateCombinationService.save(templateCombination);
        return LabelVO.builder().id(templateCombination.getId()).name(combinationName).build();
    }

    /**
     *
     * @param region 作业地域
     * @param industryList 作业行业列表
     * @param tag 模板标签
     * @param regionCode 模板地域code
     * @param industryCode 模板行业code
     * @param industryMap 行业map
     * @return 是否匹配
     */
    public static boolean templateFilter(String region, List<String> industryList, String tag, String regionCode, String industryCode, Map<String, String> industryMap) {
        if (StrUtil.isNotEmpty(regionCode) && StrUtil.isNotEmpty(industryCode)) {
            List<String> industryStr = StrUtil.split(industryCode, StrUtil.COMMA).stream().map(industryMap::get).collect(Collectors.toList());
            if (StrUtil.equals(regionCode, "100000") && industryStr.contains(COMMON_INDUSTRY_TAG)) {
                return true;
            }
            boolean b = StrUtil.split(region, ";").stream().anyMatch(r -> r.contains(RegionCodeCacheHolder.getNameByCode(regionCode)));
            if (b && industryStr.stream().anyMatch(industryList::contains)) {
                return true;
            }
            if (b) {
                return industryStr.contains(COMMON_INDUSTRY_TAG);
            }
            if (industryStr.stream().anyMatch(industryList::contains)) {
                return RegionCodeCacheHolder.getNameByCode(regionCode).contains(COMMON_REGION_TAG);
            }
            return false;
        } else {
            List<String> tags = StrUtil.split(tag, StrUtil.COMMA);
            // 全国全行业
            if (tag.contains(COMMON_REGION_TAG) && tag.contains(COMMON_INDUSTRY_TAG)) {
                return true;
            }
            // 作业地域和行业都包含
            if (tags.stream().anyMatch(region::contains) && tags.stream().anyMatch(industryList::contains)) {
                return true;
            }
            // 包含当前作业地域 且包含全行业
            if (tags.stream().anyMatch(region::contains)) {
                return tag.contains(COMMON_INDUSTRY_TAG);
            }
            if (tags.stream().anyMatch(industryList::contains)) {
                // 包含当前作业行业 且包含全地域
                return tag.contains(COMMON_REGION_TAG);
            }
            return false;
        }
    }

    @Override
    public Boolean operationType(String operationId) {
        QueryWrapper<DynamicProcessTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        return dynamicProcessTreeMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 报告生成下载
     *
     * @param response request
     * @Date 2022/7/14 17:10
     */
    @Override
    public void exportWord(HttpServletResponse response, RequestModel<ExportOperationWordDto> dto) throws IOException {

        //获取模板地址，注意k8s无法识别中文文件名，中文文件失效
        ClassPathResource classPathResource = new ClassPathResource("template/DASReportExportTemplate.docx");
        InputStream inputStream = classPathResource.getInputStream();

        //数据模型
        Map<String, Object> model = new HashMap<>();

        //准备数据
        String operationId = dto.getPrivator().getOperationId();
        //1、2阶段查询
        QueryProjectOperationExportVo poVo = coOperationMapper.queryOperationExport(dto.getPrivator().getOperationId());

        //文本
        model.put("customerName", poVo.getCustomerName());
        model.put("projectName", poVo.getProjectName());
        model.put("date", LocalDate.now());
        model.put("relatedSystem", StringUtils.replace(poVo.getRelatedSystem(),",","、"));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        model.put("createTime", simpleDateFormat.format(poVo.getCreateTime()));
        model.put("completedDate", simpleDateFormat.format(poVo.getCompletedDate()));
        model.put("customerDirector", poVo.getCustomerDirector());
        model.put("projectManager", poVo.getProjectManager());
        model.put("executor", poVo.getExecutorAccount());
        model.put("reviewer", poVo.getReviewerAccount());


        //文本3.1.2数据资产分析
        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getPrivator().getOperationId());
        List<CoInventory> coInventoryList = coInventoryService.list(queryWrapper);
        model.put("relatedSystemNum",coInventoryList.size());
        //数据资产总条数
        int dataAssetsTotal = coInventoryList.size();
        //高敏感资产
        List<CoInventory> highSensitiveAssetsList = coInventoryList.stream().filter(p -> p.getSensitive().intValue() == NumEnum.ONE.getCode()).collect(Collectors.toList());
        //高敏感资产占比
        double highSensitiveAssetsProportion = Arith.round(Double.valueOf(highSensitiveAssetsList.size()) / Double.valueOf(dataAssetsTotal) * 100, 2);
        model.put("dataAssetsNum", dataAssetsTotal);
        model.put("highSensitiveAssetsNum",highSensitiveAssetsList.size());
        model.put("highSensitiveAssetsProportion", highSensitiveAssetsProportion);


        //3.2.2 高危数据权限分析
        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(operationId);
        requestModel.setPrivator(operationIdDto);
        List<ReportAuthorityStatusQuoVo> statusQuoList = coAuthorityService.queryStatusQuo(requestModel);
        int crudTableNum = 0;
        int insertTableNum = 0;
        int deleteTableNum = 0;
        int dropTableNum = 0;
        int updateTableNum = 0;
        int selectTableNum = 0;
        int usernameTotal = 0;
        for (ReportAuthorityStatusQuoVo s : statusQuoList) {
            if(Objects.equals(s.getName(), ReportAuthorityPriEnum.CRUD_TABLE.getInfo())){
                crudTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.INSERT_TABLE.getInfo())){
                insertTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.DELETE_TABLE.getInfo())){
                deleteTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.DROP_TABLE.getInfo())){
                dropTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.UPDATE_TABLE.getInfo())){
                updateTableNum = s.getUsernameNum();
            }else if(Objects.equals(s.getName(), ReportAuthorityPriEnum.SELECT_TABLE.getInfo())){
                selectTableNum = s.getUsernameNum();
            }
            usernameTotal = s.getTotal();
        }

        double crudTableNumProportion = usernameTotal > 0 ?  Double.valueOf(crudTableNum)/Double.valueOf(usernameTotal) : 0;
        double dropTableNumProportion = usernameTotal > 0 ?  Double.valueOf(dropTableNum)/Double.valueOf(usernameTotal) : 0;
        double deleteTableNumProportion = usernameTotal > 0 ?  Double.valueOf(deleteTableNum)/Double.valueOf(usernameTotal) : 0;
        double updateTableNumProportion = usernameTotal > 0 ?  Double.valueOf(updateTableNum)/Double.valueOf(usernameTotal) : 0;
        double insertTableNumProportion = usernameTotal > 0 ?  Double.valueOf(insertTableNum)/Double.valueOf(usernameTotal) : 0;
        double selectTableNumProportion = usernameTotal > 0 ?  Double.valueOf(selectTableNum)/Double.valueOf(usernameTotal) : 0;
        model.put("usernameTotal",usernameTotal);
        model.put("crudTableNum", crudTableNum);
        model.put("dropTableNum", dropTableNum);
        model.put("deleteTableNum", deleteTableNum);
        model.put("updateTableNum", updateTableNum);
        model.put("insertTableNum", insertTableNum);
        model.put("selectTableNum", selectTableNum);
        model.put("crudTableNumProportion", Arith.round(crudTableNumProportion * 100, 2));
        model.put("dropTableNumProportion", Arith.round(dropTableNumProportion * 100, 2));
        model.put("deleteTableNumProportion", Arith.round(deleteTableNumProportion * 100, 2));
        model.put("updateTableNumProportion", Arith.round(updateTableNumProportion * 100, 2));
        model.put("insertTableNumProportion", Arith.round(insertTableNumProportion * 100, 2));
        model.put("selectTableNumProportion", Arith.round(selectTableNumProportion * 100, 2));


        //敏感数据占比图表 从入参中获取
        List<ExportWordChart> chartList = dto.getPrivator().getChartList();
        List<ExportWordChart> sensitiveChartList = chartList.stream().filter(s -> "敏感数据占比图表".equals(s.getName())).collect(Collectors.toList());

        String imageBase64Data = sensitiveChartList.get(0).getPicture();
        imageBase64Data = imageBase64Data.substring(IMAGE_PNG_BASE64.length());
        //base64解码
        byte[] decode = Base64.decode(imageBase64Data);
        ByteArrayInputStream bis = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis1 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis2 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis3 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis4 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis5 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis6 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis7 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis8 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis9 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis10 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis11 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis12 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis13 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis14 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis15 = new ByteArrayInputStream(decode);
        ByteArrayInputStream bis16 = new ByteArrayInputStream(decode);


        model.put("sensitiveAssetsProportionImg", Pictures.ofStream(bis, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeAddImg", Pictures.ofStream(bis1, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeUpdateImg", Pictures.ofStream(bis2, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeSelectImg", Pictures.ofStream(bis3, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeDeleteImg", Pictures.ofStream(bis4, PictureType.PNG)
                .size(600, 200).create());
        model.put("userAuthorityTypeDropImg", Pictures.ofStream(bis5, PictureType.PNG)
                .size(600, 200).create());


        //3.2.4	用户-资产权限分布分析
        List<ReportAuthorityUserAssetVo> reportAuthorityUserAssetVos = coAuthorityService.queryUserAssetsResult(requestModel);
        AtomicInteger i = new AtomicInteger();
        reportAuthorityUserAssetVos.stream().forEach(p -> {
            p.setSort(i.getAndIncrement()+1);
        });
        model.put("userAssetAuthorityList", reportAuthorityUserAssetVos);


        //4.1.1	评估概述
        //查询现状核验表获取标签页
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", operationId);
        query.eq("level", "3");
        List<CoVerification> coVerifications = coVerificationService.list(query);
        List<String> tabNameList = coVerifications.stream().map(v -> v.getType()).distinct().collect(Collectors.toList());
        String tabNames = StringUtils.join(tabNameList.toArray(), "、");

        model.put("modelName", "综合模型");
        model.put("level", "3级 充分定义");
        model.put("tabNames", tabNames);

        //4.1.2.1	评估结果{{chartName}}
//        model.put("chartName", dto.getPrivator().getChartName());
        model.put("chartNameImg", Pictures.ofStream(bis6, PictureType.PNG)
                .size(600, 200).create());

        //查询图表备注
        QueryWrapper<CoGapAnalysis> query0 = new QueryWrapper<>();
        query0.eq("operation_id", dto.getPrivator().getOperationId());
//        query0.eq("chart_name", dto.getPrivator().getChartName());
        List<CoGapAnalysis> coGapAnalysisList = coGapAnalysisService.list(query0);
        String chartRemark = StringUtils.isNotBlank(coGapAnalysisList.get(0).getRemark()) ? coGapAnalysisList.get(0).getRemark() : "";
        model.put("chartRemark", chartRemark);

        //4.1.3.1	{{tabName1}}安全现状
        QueryWrapper<CoVerification> query1 = new QueryWrapper<>();
        query1.eq("operation_id", dto.getPrivator().getOperationId());
        query1.eq("type", tabNameList.get(0));
        List<CoVerification> tabList1 = coVerificationService.list(query1);
        int sort1 = 1;
        for (CoVerification coVerification : tabList1){
            coVerification.setSort(sort1++);
        }
        model.put("tabName1", tabNameList.get(0));
        model.put("tabList1", tabList1);

        QueryWrapper<CoVerification> query2 = new QueryWrapper<>();
        query2.eq("operation_id", dto.getPrivator().getOperationId());
        query2.eq("type", tabNameList.get(1));
        List<CoVerification> tabList2 = coVerificationService.list(query2);
        int sort2 = 1;
        for (CoVerification coVerification : tabList2){
            coVerification.setSort(sort2++);
        }

        model.put("tabName2", tabNameList.get(1));
        model.put("tabList2", tabList2);



        //5.1.2	评估分析
        QueryWrapper<CoBasicEn> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("operation_id", dto.getPrivator().getOperationId());
        List<CoBasicEn> coBasicEns = coBasicEnService.list(queryWrapper1);
        //高危险漏洞数量
        List<CoBasicEn> highList = coBasicEns.stream().filter(p -> DangerLevelEnum.isHigh(p.getDangerLevel())).collect(Collectors.toList());
        //中危险漏洞数量
        List<CoBasicEn> mediumList = coBasicEns.stream().filter(p -> DangerLevelEnum.isMedium(p.getDangerLevel())).collect(Collectors.toList());
        model.put("loopholeTotal", coBasicEns.size());
        model.put("highNum", highList.size());
        model.put("mediumNum", mediumList.size());
        model.put("loopholAnalysisResultImg", Pictures.ofStream(bis7, PictureType.PNG)
                .size(600, 200).create());
        model.put("loopholByDBIPResultImg", Pictures.ofStream(bis8, PictureType.PNG)
                .size(600, 200).create());

        //*******	漏洞统计分析
        RequestModel<CommonDto> commonModel = new RequestModel<>();
        CommonDto commonDto = new CommonDto();
        commonDto.setOperationId(dto.getPrivator().getOperationId());
        commonDto.setLabelId(LabelEnum.JCHJ.getCode());
        commonModel.setPrivator(commonDto);
        List<QueryViewLoopholeBySystemVo> loopholeByIPSystemList = coViewCustomerService.queryLoopholeBySystem(commonModel);
        model.put("loopholeByIPSystemList", loopholeByIPSystemList);
        AtomicInteger sort = new AtomicInteger(1);
        loopholeByIPSystemList.stream().forEach(l -> {
            l.setSort(sort.getAndIncrement());
        });


        //*******	合规风险分析
        QueryViewLegalResultVo legalResultVo = coViewCustomerService.queryLegalProportion(requestModel);
        model.put("lawTotalNum", legalResultVo.getLawTotalNum());
        model.put("lawDocNum", legalResultVo.getLawDocNum());
        model.put("ruleDocNum", legalResultVo.getRuleDocNum());
        model.put("standardDocNum", legalResultVo.getStandardDocNum());
        model.put("itemTotalNum", legalResultVo.getItemTotalNum());
        model.put("qualifiedProportion", legalResultVo.getQualifiedProportion());
        model.put("countANum", legalResultVo.getCountANum());
        model.put("countBNum", legalResultVo.getCountBNum());
        model.put("countCNum", legalResultVo.getCountCNum());
        model.put("countDNum", legalResultVo.getCountDNum());
        model.put("countAProportion", legalResultVo.getCountAProportion());
        model.put("countBProportion", legalResultVo.getCountBProportion());
        model.put("countCProportion", legalResultVo.getCountCProportion());
        model.put("countDProportion", legalResultVo.getCountDProportion());

        model.put("legalResultImg", Pictures.ofStream(bis9, PictureType.PNG)
                .size(600, 200).create());

        //5.2.3	具体评估结果 5.2.3.1	{{co_legal.law_name}}评估详情
        QueryWrapper<CoLegal> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("operation_id", dto.getPrivator().getOperationId());
        List<CoLegal> coLegalList = coLegalService.list(queryWrapper2);
        String lawName = coLegalList.get(0).getLawName();
        queryWrapper2.eq("law_name", lawName);
        List<CoLegal> legalList = coLegalService.list(queryWrapper2);
        model.put("lawName", lawName);
        model.put("legalList", legalList);

        //5.3.3.1	总体风险分析
        QueryWrapper<CoLifecycle> queryWrapper3 = new QueryWrapper<>();
        queryWrapper3.eq("operation_id", dto.getPrivator().getOperationId());
        queryWrapper3.eq("label_id", LabelEnum.SMZQ.getCode());
        List<CoLifecycle> coLifecycleList = coLifecycleService.list(queryWrapper3);
        List<String> systemList = coLifecycleList.stream().map(p -> p.getBusSystem()).distinct().collect(Collectors.toList());

        List<QueryViewLifecycleSystemResultVo> voList = new ArrayList<>();
        systemList.stream().forEach(p -> {
            QueryViewLifecycleSystemResultVo result = coViewCustomerService.getLifecycleSingleResult(dto.getPrivator().getOperationId(), p);
            voList.add(result);
        });
        int busSystemNum = systemList.size();
        int riskTotal = voList.stream().mapToInt(p -> p.getRiskTotal()).sum();
        int highRiskAssetNum = voList.stream().mapToInt(p -> p.getHighRiskAssetNum()).sum();
        int mediumRiskAssetNum = voList.stream().mapToInt(p -> p.getMediumRiskAssetNum()).sum();
        int moreThanMediumNum = voList.stream().mapToInt(p -> p.getMoreThanMediumNum()).sum();
        double highRiskAssetNumProportion = Double.valueOf(highRiskAssetNum) / Double.valueOf(riskTotal) * 100;
        double mediumRiskAssetNumProportion = Double.valueOf(mediumRiskAssetNum) / Double.valueOf(riskTotal) * 100;
        double moreThanMediumNumProportion = Double.valueOf(moreThanMediumNum) / Double.valueOf(riskTotal) * 100;
        model.put("busSystemNum", busSystemNum);
        model.put("highRiskAssetNum", highRiskAssetNum);
        model.put("mediumRiskAssetNum", mediumRiskAssetNum);
        model.put("highRiskAssetNumProportion", highRiskAssetNumProportion);
        model.put("mediumRiskAssetNumProportion", mediumRiskAssetNumProportion);
        model.put("moreThanMediumNumProportion", moreThanMediumNumProportion);

        model.put("queryLifecycleReslutImg", Pictures.ofStream(bis10, PictureType.PNG)
                .size(600, 200).create());

        RequestModel<OperationIdDto> requestModel2 = new RequestModel<>();
        OperationIdDto operationIdDto1 = new OperationIdDto();
        operationIdDto1.setOperationId(dto.getPrivator().getOperationId());
        requestModel2.setPrivator(operationIdDto1);
        List<QueryViewLifecycleSystemResultVo> voList1 = coViewCustomerService.queryLifecycleResultList(requestModel2);
        OptionalDouble singSystemFactorMax = voList1.stream().mapToDouble(p -> p.getSingleSystemFactor().doubleValue()).max();
        model.put("singSystemFactorMax", singSystemFactorMax.getAsDouble());
        model.put("queryLifecycleReslutListImg", Pictures.ofStream(bis11, PictureType.PNG)
                .size(600, 200).create());


        //5.3.3.3	总体生命周期风险分析
        RequestModel<QueryViewLifecycleAnalysisDto> requestModel3 = new RequestModel<>();
        QueryViewLifecycleAnalysisDto  queryViewLifecycleAnalysisDto = new QueryViewLifecycleAnalysisDto();
        queryViewLifecycleAnalysisDto.setOperationId(dto.getPrivator().getOperationId());
        queryViewLifecycleAnalysisDto.setBusSystem("汇总分析");
        requestModel3.setPrivator(queryViewLifecycleAnalysisDto);
        QueryViewLifecycleAnalysisVo queryViewLifecycleAnalysisVo = coViewCustomerService.queryLifecycleAnalysis(requestModel3);

        List<Double> list = new ArrayList<>();
        list.add(queryViewLifecycleAnalysisVo.getCollectionRiskFactor().doubleValue());
        list.add(queryViewLifecycleAnalysisVo.getTransmissionRiskFactor().doubleValue());
        list.add(queryViewLifecycleAnalysisVo.getStorageRiskFactor().doubleValue());
        list.add(queryViewLifecycleAnalysisVo.getProcessingRiskFactor().doubleValue());
        list.add(queryViewLifecycleAnalysisVo.getExchangeRiskFactor().doubleValue());
        list.add(queryViewLifecycleAnalysisVo.getDestructionRiskFactor().doubleValue());
        list.add(queryViewLifecycleAnalysisVo.getCommonRiskFactor().doubleValue());
        Double max = Collections.max(list);

        Map<String, Double> map = new HashMap<>();
        map.put("采集阶段", queryViewLifecycleAnalysisVo.getCollectionRiskFactor().doubleValue());
        map.put("传输阶段", queryViewLifecycleAnalysisVo.getTransmissionRiskFactor().doubleValue());
        map.put("存储阶段", queryViewLifecycleAnalysisVo.getStorageRiskFactor().doubleValue());
        map.put("处理阶段", queryViewLifecycleAnalysisVo.getProcessingRiskFactor().doubleValue());
        map.put("交换阶段", queryViewLifecycleAnalysisVo.getExchangeRiskFactor().doubleValue());
        map.put("销毁阶段", queryViewLifecycleAnalysisVo.getDestructionRiskFactor().doubleValue());
        map.put("通用阶段", queryViewLifecycleAnalysisVo.getCommonRiskFactor().doubleValue());
        String stageName = "";
        for (String key : map.keySet()) {
            if(map.get(key).equals(max)){
                stageName = key;
            }
        }
        model.put("stageName", stageName);


        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder().bind("userAssetAuthorityList", policy)
                .bind("tabList1", policy)
                .bind("tabList2", policy)
                .bind("loopholeByIPSystemList", policy)
                .bind("legalList", policy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);


        //输出结果
        OutputStream out = response.getOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(out);
        template.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, bos, out);
    }

    @Override
    @SchemaSwitch(String.class)
    public OperationContentVO detail(String operationId) {
        OperationContentVO operationDetail = coOperationMapper.operationDetail(operationId);
        operationDetail.setScanEnable(StrUtil.isNotEmpty(sysConfigService.selectConfigByKey(SysConfigEnum.SCAN_TOKEN.getKey())));
        // 组合模板名称
        if (StrUtil.isEmpty(operationDetail.getTemplateName())) {
            TemplateCombination templateCombination = templateCombinationService.getBaseMapper().selectById(Long.parseLong(operationDetail.getTemplateId()));
            String templateNames = templateMapper.selectList(new QueryWrapper<Template>().in("id", Arrays.asList(templateCombination.getTemplateIds()))).stream()
                    .map(Template::getName).collect(Collectors.joining(StrUtil.LF));
            operationDetail.setTemplateName(templateNames);
        }
        // 能力模块名称
        if (StrUtil.isNotEmpty(operationDetail.getAbilityModule())) {
            operationDetail.setAbilityModule(AbilityType.getSelectedCodes(Integer.parseInt(operationDetail.getAbilityModule())));
        }
        final StringBuilder sb = new StringBuilder();
        List<String> services = StrUtil.split(operationDetail.getServiceContent(), StrUtil.COMMA);
        List<LabelEnum> label = new ArrayList<>();
        label.add(LabelEnum.WJDY);
        label.add(LabelEnum.ZCSL);
        label.add(LabelEnum.AQXZ);
        label.add(LabelEnum.FXPG);
        label.add(LabelEnum.AQJY);
        QueryWrapper<DynamicProcessTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        Map<Long, List<DynamicProcessTree>> groupTrees = dynamicProcessTreeMapper.selectList(queryWrapper).stream()
                .collect(Collectors.groupingBy(DynamicProcessTree::getParentId));
        if (CollUtil.isNotEmpty(groupTrees)) {
            for (LabelEnum l : label) {
                List<DynamicProcessTree> dynamicProcessTrees = groupTrees.get(l.getCode());
                if (CollUtil.isNotEmpty(dynamicProcessTrees)) {
                    dynamicProcessTrees.stream().filter(t -> services.contains(t.getTreeId().toString()))
                            .sorted(Comparator.comparing(DynamicProcessTree::getOrderNum))
                            .forEach(t -> sb.append(t.getTreeName()).append(StrUtil.COMMA));
                }
            }
        } else {
            for (LabelEnum l : label) {
                List<LabelEnum> processTrees = LabelEnum.getByParentCode(l.getCode());
                if (CollUtil.isNotEmpty(processTrees)) {
                    processTrees.stream().filter(t -> services.contains(t.getCode().toString()))
                            .forEach(t -> sb.append(t.getName()).append(StrUtil.COMMA));
                }
            }
        }
        if (operationDetail.getSpecId() != null) {
            operationDetail.setSpecContent(
                specialEvaluationConfigMapper.selectById(operationDetail.getSpecId()).getEvaluationContent());
        } else {
            operationDetail.setIsSpec(false);
        }
        operationDetail.setServiceContentName(sb.substring(0, sb.length() - 1));

        // 赋值业务系统标签
        List<BusSystemTagDTO> busSystemTagS = iMidOperationService.getBusSystemTagsByOperationId(operationId);
        operationDetail.setBusSystemTags(busSystemTagS);
        return operationDetail;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public OperationCopyVO copyJob(RequestModel<OperationCopyDTO> dto) {
        // 保存作业基本信息
        OperationAddDTO addDTO = dto.getPrivator();
        List<String> serviceContent = StrUtil.split(addDTO.getServiceContent(), StrUtil.COMMA);
        if (serviceContent.stream().anyMatch(content -> content.startsWith(LabelEnum.XTDY.getCode().toString()))) {
            serviceContent.removeIf(content -> content.startsWith(LabelEnum.XTDY.getCode().toString()));
            serviceContent.add(LabelEnum.XTDY.getCode().toString());
            addDTO.setServiceContent(StrUtil.join(StrUtil.COMMA, serviceContent));
        }
        RequestModel<OperationAddDTO> model = new RequestModel<>();
        model.setPrivator(addDTO);
        String operationId = dto.getPrivator().getOperationId();
        CoOperation old = coOperationMapper.selectById(operationId);
        String newId  = add(model, old.getVersion());

        Map<TreeLabelDTO, TreeLabelDTO> newLabelMap = getNewLabelMap(dto.getPrivator().getBusSystemMap(), operationId, newId);
        List<Long> objectIds = newLabelMap.keySet().stream().map(TreeLabelDTO::getTreeId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(objectIds))
            objectIds.add(LabelEnum.ZZDY.getCode());
        // 新旧作业个人信息不适用按钮是否选择一致。都适用或者都不适用，否则与调研问卷相关的一些内容不予复制
        boolean personalSame = Objects.equals(old.getInapplicable(), dto.getPrivator().getInapplicable());

        // 通过作业ID复制问卷数据
        questionnaireMapper.copyQuestionnaireByOperationId(operationId, newId, objectIds);
        // 通过作业ID复制答卷数据
        questionnaireContentService.getBaseMapper().copyQuestionnaireContentByOperationId(operationId, newId, objectIds);
        // 汽车行业复制核查项符合程度
        questionnaireItemDegreeService.copyQuestionnaireItemDegreeByOperationId(operationId, newId);

        // 复制作业附件
        Map<String, String> fileMap = new HashMap<>();
        QueryWrapper<QuestionnaireContent> fileQuery = new QueryWrapper<>();
        fileQuery.select("file_ids");
        fileQuery.eq("operation_id", operationId);
        List<QuestionnaireContent> questionnaireContents = questionnaireContentService.getBaseMapper().selectList(fileQuery);
        questionnaireContents.stream().filter(q -> Objects.nonNull(q) && StrUtil.isNotEmpty(q.getFileIds())).flatMap(q ->
                StrUtil.split(q.getFileIds(), StrUtil.COMMA).stream()).forEach(fileId -> {
            String id = SnowFlake.getId();
            fileMap.put(fileId, id);
        });
        if (CollUtil.isNotEmpty(fileMap)) {
            List<CoFile> coFiles = coFileService.list(new QueryWrapper<CoFile>().in("file_id", fileMap.keySet()));
            coFiles.forEach(file -> {
                file.setFileId(fileMap.get(file.getFileId()));
                file.setOperationId(newId);
            });
            coFileService.saveBatch(coFiles);
            QueryWrapper<QuestionnaireContent> contentQueryWrapper = new QueryWrapper<>();
            contentQueryWrapper.select("id, file_ids");
            contentQueryWrapper.eq("operation_id", newId);
            List<QuestionnaireContent> list = questionnaireContentService.list(contentQueryWrapper);
            List<QuestionnaireContent> updateContents = list.stream().peek(l ->
                    l.setFileIds(
                            StrUtil.split(l.getFileIds(), StrUtil.COMMA).stream().map(fileMap::get)
                                    .filter(StrUtil::isNotEmpty).collect(Collectors.joining(StrUtil.COMMA))
                    )).collect(Collectors.toList());
            questionnaireContentService.updateBatchById(updateContents);
        }

        final Map<String, TreeLabelDTO> LabelNameMap = newLabelMap.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey().getTreeName(), Map.Entry::getValue));
        if (personalSame) {
            // 通过作业ID复制现状核验信息
            List<CoVerification> verifications =
                    coVerificationService.list(new QueryWrapper<CoVerification>().eq("operation_id", operationId));
            if (CollUtil.isNotEmpty(verifications)) {
                verifications.forEach(coVerification -> {
                    coVerification.setVerificationId(SnowFlake.getId());
                    coVerification.setOperationId(newId);
                    if (CollUtil.isNotEmpty(LabelNameMap)) {
                        String systemResult = coVerification.getSystemResult();
                        OptEnum score = OptEnum.D;
                        List<String> newSystemResult = new ArrayList<>();
                        List<String> split = StrUtil.split(systemResult, StrUtil.LF).stream().filter(StrUtil::isNotEmpty).collect(Collectors.toList());
                        for (String s : split) {
                            String systemName = StrUtil.subBefore(s, "系统：", true);
                            // 复制作业可能删除业务系统
                            if (LabelNameMap.containsKey(systemName)) {
                                String res = StrUtil.subAfter(s, "系统：", true);
                                newSystemResult.add(LabelNameMap.get(systemName).getTreeName() + "系统：" + res);
                                OptEnum scoreByInfo = OptEnum.getScoreByInfo(res);
                                if (scoreByInfo.getScore() > score.getScore()) {
                                    score = scoreByInfo;
                                } else if (Objects.equals(scoreByInfo.getInfo(), OptEnum.C.getInfo())){
                                    score = OptEnum.C;
                                }
                            }
                        }
                        coVerification.setScore(BigDecimal.valueOf(score.getScore()));
                        coVerification.setResult(score.getInfo());
                        coVerification.setSystemResult(String.join(StrUtil.LF, newSystemResult));
                    }
                });
            }
            coVerificationService.saveBatch(verifications);

            if (old.getIsSpec() != null && old.getIsSpec()) {
                //复制专项评估BP结果
                specCalcResultMapper.copySpecCalcResult(operationId, newId);
            }
        }


        final Map<Long, TreeLabelDTO> LabelIdMap = newLabelMap.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey().getTreeId(), Map.Entry::getValue));
        // 通过作业ID复制资产盘点业务信息
        // 如果定性转定量或者定量转定性，则无需复制资产信息
        if (old.getAssetType().equals(addDTO.getAssetType())) {
            List<DiscoveryClassifyData> discoveryClassifyDataList = new ArrayList<>();
            List<CoInventory> coInventoryList =
                coInventoryService.list(new QueryWrapper<CoInventory>().eq("operation_id", operationId));
            Map<String, List<DiscoveryClassifyData>> inventoryAssetGroup = discoveryClassifyDataMapper.selectList(
                    new QueryWrapper<DiscoveryClassifyData>().eq("operation_id", operationId)).stream().collect(Collectors.groupingBy(DiscoveryClassifyData::getInventoryId));
            if (CollUtil.isNotEmpty(coInventoryList)) {
                Iterator<CoInventory> iterator = coInventoryList.iterator();
                while (iterator.hasNext()) {
                    CoInventory coInventory = iterator.next();
                    if (CollUtil.isNotEmpty(LabelIdMap) && !LabelIdMap.containsKey(coInventory.getSystemId())) {
                        iterator.remove();
                        continue;
                    }
                    String inventoryId = SnowFlake.getId();
                    List<DiscoveryClassifyData> classifyDataList = inventoryAssetGroup.get(coInventory.getInventoryId());
                    if (CollUtil.isNotEmpty(classifyDataList)) {
                        classifyDataList.forEach(d -> {
                            d.setId(null);
                            d.setOperationId(newId);
                            d.setInventoryId(inventoryId);
                        });
                        discoveryClassifyDataList.addAll(classifyDataList);
                    }
                    coInventory.setInventoryId(inventoryId);
                    coInventory.setOperationId(newId);
                    if (coInventory.getDbConfig() != null) {
                        coInventory.setDbConfig(JSON.parseObject(coInventory.getDbConfig().toString(), DbConfig.class));
                    }
                    if (CollUtil.isNotEmpty(LabelIdMap))
                        coInventory.setBusSystem(LabelIdMap.get(coInventory.getSystemId()).getTreeName());
                }
            }
            coInventoryService.saveBatch(coInventoryList);
            PartitionUtils.part(discoveryClassifyDataList, discoveryClassifyDataMapper::insertList);
        }

        // 定性资产复制
        if (Objects.equals(old.getAssetType(), addDTO.getAssetType()) && old.getAssetType() == 2) {
            List<DetermineAssetType> determineAssetTypeList = determineAssetTypeService
                .list(new QueryWrapper<DetermineAssetType>().eq("operation_id", operationId));
            Iterator<DetermineAssetType> TypeIterator = determineAssetTypeList.iterator();
            while (TypeIterator.hasNext()) {
                DetermineAssetType determineAssetType = TypeIterator.next();
                if (CollUtil.isNotEmpty(LabelIdMap) && !LabelIdMap.containsKey(determineAssetType.getSystemId())) {
                    TypeIterator.remove();
                    continue;
                }
                determineAssetType.setId(null);
                determineAssetType.setOperationId(newId);
                if (CollUtil.isNotEmpty(LabelIdMap))
                    determineAssetType.setSystemId(LabelIdMap.get(determineAssetType.getSystemId()).getTreeId());
            }
            List<DetermineAssetData> determineAssetDataList = determineAssetDataService
                .list(new QueryWrapper<DetermineAssetData>().eq("operation_id", operationId));
            Iterator<DetermineAssetData> dataIterator = determineAssetDataList.iterator();
            while (dataIterator.hasNext()) {
                DetermineAssetData determineAssetData = dataIterator.next();
                if (CollUtil.isNotEmpty(LabelIdMap) && !LabelIdMap.containsKey(determineAssetData.getSystemId())) {
                    dataIterator.remove();
                }
                determineAssetData.setId(null);
                determineAssetData.setOperationId(newId);
                if (CollUtil.isNotEmpty(LabelIdMap))
                    determineAssetData.setSystemId(LabelIdMap.get(determineAssetData.getSystemId()).getTreeId());
            }
            determineAssetTypeService.saveBatch(determineAssetTypeList);
            determineAssetDataService.saveBatch(determineAssetDataList);
        }

        // 复制数据权限
        List<CoAssetView> coAssetViewList =
                coAssetViewService.list(new QueryWrapper<CoAssetView>().eq("operation_id", operationId));
        List<CoAssetUser> coAssetUserList =
                coAssetUserService.list(new QueryWrapper<CoAssetUser>().eq("operation_id", operationId));
        List<CoAssetUser> copyUserList = new ArrayList<>(coAssetUserList.size());
        Map<String, List<CoAssetUser>> assetUserGroup = coAssetUserList.stream().collect(Collectors.groupingBy(CoAssetUser::getAssetId));
        Iterator<CoAssetView> viewIterator = coAssetViewList.iterator();
        while (viewIterator.hasNext()) {
            CoAssetView coAssetView = viewIterator.next();
            if (CollUtil.isNotEmpty(LabelNameMap) && !LabelNameMap.containsKey(coAssetView.getBusSystem())) {
                viewIterator.remove();
                continue;
            }
            String viewId = SnowFlake.getId();
            List<CoAssetUser> coAssetUsers = assetUserGroup.get(coAssetView.getViewId());
            if (CollUtil.isNotEmpty(coAssetUsers)) {
                copyUserList.addAll(coAssetUsers.stream().peek(u -> {
                    u.setId(null);
                    u.setOperationId(newId);
                    u.setAssetId(viewId);
                    u.setDbConfig(JSON.parseObject(u.getDbConfig().toString(), DbConfig.class));
                }).collect(Collectors.toList()));
            }
            coAssetView.setViewId(viewId);
            coAssetView.setOperationId(newId);
            if (CollUtil.isNotEmpty(LabelNameMap))
                coAssetView.setBusSystem(LabelNameMap.get(coAssetView.getBusSystem()).getTreeName());
            coAssetView.setDbConfig(JSON.parseObject(coAssetView.getDbConfig().toString(), DbConfig.class));
        }
        coAssetViewService.saveBatch(coAssetViewList, 1000);
        coAssetUserService.saveBatch(copyUserList, 1000);

        if (personalSame) {
            // 通过作业ID复制差距分析业务信息
            List<CoGapAnalysis> gapAnalyses =
                    coGapAnalysisService.list(new QueryWrapper<CoGapAnalysis>().eq("operation_id", operationId));
            gapAnalyses.forEach(coGapAnalysis -> {
                coGapAnalysis.setGapId(SnowFlake.getId());
                coGapAnalysis.setOperationId(newId);
            });
            coGapAnalysisService.saveBatch(gapAnalyses);
        }


        // 通过作业ID复制基础评估业务信息
        List<DetectionResult> detectionResults = detectionResultService.list(new QueryWrapper<DetectionResult>().eq("operation_id", operationId));
        detectionResults.forEach(detectionResult -> {
            detectionResult.setId(null);
            detectionResult.setJobId(-1L);
            detectionResult.setOperationId(newId);
        });
        detectionResultService.saveBatch(detectionResults);

        if (personalSame) {
            // 通过作业ID复制合法合规业务信息
            List<CoLegal> coLegalList = coLegalService.list(new QueryWrapper<CoLegal>().eq("operation_id", operationId));
            // 查询业务系统结果
            List<CoSystemResult> coSystemResultList = iCoSystemResultService.list(new QueryWrapper<CoSystemResult>().eq("operation_id", operationId));
            Map<String, List<CoSystemResult>> legalSystemResultMap = coSystemResultList.stream().collect(Collectors.groupingBy(CoSystemResult::getRelId));
            Iterator<CoLegal> iterator = coLegalList.iterator();
            List<CoSystemResult> newSystemResultList = new ArrayList<>();
            while (iterator.hasNext()) {
                CoLegal coLegal = iterator.next();
                List<CoSystemResult> newSystemList = legalSystemResultMap.get(coLegal.getLegalId());
                coLegal.setLegalId(SnowFlake.getId());
                coLegal.setOperationId(newId);
                if (CollUtil.isNotEmpty(newSystemList)){
                    newSystemList.forEach(coSystemResult -> {
                        coSystemResult.setRelId(coLegal.getLegalId());
                        coSystemResult.setOperationId(newId);
                        newSystemResultList.add(coSystemResult);
                    });
                }
                if (CollUtil.isNotEmpty(LabelNameMap)) {
                    String systemResult = coLegal.getSystemResult();
                    OptEnum score = OptEnum.D;
                    List<String> newSystemResult = new ArrayList<>();
                    List<String> split = StrUtil.split(systemResult, StrUtil.LF).stream().filter(StrUtil::isNotEmpty).collect(Collectors.toList());
                    for (String s : split) {
                        String systemName = StrUtil.subBefore(s, "系统：", true);
                        // 复制作业可能删除业务系统
                        if (LabelNameMap.containsKey(systemName)) {
                            String res = StrUtil.subAfter(s, "系统：", true);
                            newSystemResult.add(LabelNameMap.get(systemName).getTreeName() + "系统：" + res);
                            OptEnum scoreByInfo = OptEnum.getScoreByInfo(res);
                            if (scoreByInfo.getScore() > score.getScore()) {
                                score = scoreByInfo;
                            } else if (Objects.equals(scoreByInfo.getInfo(), OptEnum.C.getInfo())){
                                score = OptEnum.C;
                            }
                        }
                    }
                    coLegal.setScore(BigDecimal.valueOf(score.getScore()));
                    coLegal.setResult(score.getInfo());
                    coLegal.setSystemResult(String.join(StrUtil.LF, newSystemResult));
                }
            }
            coLegalService.saveBatch(coLegalList);
            iCoSystemResultService.saveBatch(newSystemResultList);
        }


        // 通过作业ID复制建议条目表信息
        Set<Long> systemIds = LabelIdMap.keySet();
        List<AdviseItem> adviseItemList = adviseItemService.list(new QueryWrapper<AdviseItem>().eq("operation_id", operationId));
        Iterator<AdviseItem> adviseItemIterator = adviseItemList.iterator();
        while (adviseItemIterator.hasNext()) {
            AdviseItem adviseItem = adviseItemIterator.next();
            if (CollUtil.isNotEmpty(LabelIdMap) && systemIds.stream().noneMatch(systemId -> adviseItem.getSystemId().contains(systemId.toString()))) {
                adviseItemIterator.remove();
                continue;
            }
            adviseItem.setId(null);
            adviseItem.setOperationId(newId);
            if (CollUtil.isNotEmpty(LabelIdMap))
                adviseItem.setSystemId(StrUtil.split(adviseItem.getSystemId(), StrUtil.COMMA).stream().map(id -> {
                    TreeLabelDTO treeLabelDTO = LabelIdMap.get(Long.parseLong(id));
                    if (Objects.nonNull(treeLabelDTO))
                        return treeLabelDTO.getTreeId().toString();
                    return null;
                }).filter(StrUtil::isNotEmpty).collect(Collectors.joining(StrUtil.COMMA)));
        }
        adviseItemService.saveBatch(adviseItemList);

        // 通过作业ID复制前台处置方标表信息
        List<AdviseScheme> adviseSchemeList = adviseSchemeService.list(new QueryWrapper<AdviseScheme>().eq("operation_id", operationId));
        Iterator<AdviseScheme> adviseSchemeIterator = adviseSchemeList.iterator();
        while (adviseSchemeIterator.hasNext()) {
            AdviseScheme adviseScheme = adviseSchemeIterator.next();
            if (CollUtil.isNotEmpty(LabelIdMap) && systemIds.stream().noneMatch(systemId -> adviseScheme.getSystemId().contains(systemId.toString()))) {
                adviseSchemeIterator.remove();
                continue;
            }
            adviseScheme.setId(null);
            adviseScheme.setOperationId(newId);
            if (CollUtil.isNotEmpty(LabelIdMap))
                adviseScheme.setSystemId(StrUtil.split(adviseScheme.getSystemId(), StrUtil.COMMA).stream().map(id -> {
                    TreeLabelDTO treeLabelDTO = LabelIdMap.get(Long.parseLong(id));
                    if (Objects.nonNull(treeLabelDTO))
                        return treeLabelDTO.getTreeId().toString();
                    return null;
                }).filter(StrUtil::isNotEmpty).collect(Collectors.joining(StrUtil.COMMA)));
        }
        adviseSchemeService.saveBatch(adviseSchemeList);

        // 通过新的作业ID查询作业信息
        OperationCopyVO operationCopyVO = coOperationMapper.queryOperationById(newId);
        //获取过程清单
        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(operationId);
        requestModel.setPrivator(operationIdDto);
        operationCopyVO.setTreeSelectList(treeSelect(requestModel));

        //查询进度
        RequestModel<PrimaryKeyDTO> requestModel1 = new RequestModel<>();
        PrimaryKeyDTO primaryKeyDTO = new PrimaryKeyDTO();
        primaryKeyDTO.setId(operationId);
        requestModel1.setPrivator(primaryKeyDTO);
        queryProgress(requestModel1);
        operationCopyVO.setProgressVo(queryProgress(requestModel1));
        return operationCopyVO;
    }

    // 修改业务类型映射，原来的业务系统映射到新的业务系统
    private Map<TreeLabelDTO, TreeLabelDTO> getNewLabelMap(Map<String, String> busSystemMap, String operationId, String newId) {
        if (CollUtil.isEmpty(busSystemMap))
            return new HashMap<>();
        Map<String, TreeLabelDTO> oldLabelMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId,
                LabelEnum.XTDY.getCode()).stream().collect(Collectors.toMap(TreeLabelDTO::getTreeName, Function.identity()));
        Map<String, TreeLabelDTO> newLabelMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(newId,
                LabelEnum.XTDY.getCode()).stream().collect(Collectors.toMap(TreeLabelDTO::getTreeName, Function.identity()));
        return busSystemMap.entrySet().stream().map(entry -> {
            TreeLabelDTO oldLabel = oldLabelMap.get(entry.getKey());
            TreeLabelDTO newLabel = newLabelMap.get(entry.getValue());
            if (oldLabel != null && newLabel != null) {
                return new AbstractMap.SimpleEntry<>(oldLabel, newLabel);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private String rename(String operationName) {
        String newName;
        int beginIndex = operationName.lastIndexOf("(");
        int endIndex = operationName.lastIndexOf(")");
        if (beginIndex>0 && endIndex>0){
            String s = operationName.substring(beginIndex+1, endIndex);
            if (isDigit(s)){
                int index = Integer.parseInt(s);
                index++;
                newName = operationName.substring(0,beginIndex)+"("+index+")";
            } else {
                newName = operationName + "-副本(1)";
            }
        } else {
            newName = operationName + "-副本(1)";
        }
        // 检验数据库中是否存在同名作业
        QueryWrapper<CoOperation> query = new QueryWrapper<>();
        query.eq("operation_name", newName);
        if(coOperationMapper.selectCount(query) > 0 ){
            newName = rename(newName);
        }
        return newName;
    }

    public static boolean isDigit(String str){
        if (StringUtils.isNotEmpty(str)) {
            return str.matches("^[0-9]*$");
        } else{
            return false;
        }
    }

    @Override
    @SchemaSwitch(value = ModelParam.class)
    public Boolean useTemplate(ModelParam modelParam) {
        //可以重复应用，先删后插入，一个作业仅应用一个模型
        operationModelMapper.delete(new QueryWrapper<OperationModel>().eq("operation_id", modelParam.getOperationId()));
        OperationModel operationModel = new OperationModel();
        BeanUtils.copyProperties(modelParam,operationModel);
        //定性资产 copy determine_asset_data数据到co_inventory表
        // 定性资产 copy determine_asset_data数据到discovery_classify_data表
        List<DetermineAssetData> list = determineAssetDataService
            .list(new QueryWrapper<DetermineAssetData>().eq("operation_id", modelParam.getOperationId()));
        Map<Long, String> busSystemMap =
            dynamicProcessTreeMapper.selectByOperationIdAndTreeId(modelParam.getOperationId(), LabelEnum.XTDY.getCode())
                .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName));
        List<CoInventory> coInventoryList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            // 先删除旧的该作业的资产数据，后保存最新的
            coInventoryService.remove(new QueryWrapper<CoInventory>().eq("operation_id", modelParam.getOperationId()));
            discoveryClassifyDataMapper.delete(new QueryWrapper<DiscoveryClassifyData>().eq("operation_id", modelParam.getOperationId()));
            List<DiscoveryClassifyData> discoveryClassifyDataList = new ArrayList<>();

            Map<Integer, List<DiscoveryBusinessDTO>> templateContentMap = new HashMap<>(16);
            list.forEach(
                determineAssetData -> {
                    List<DiscoveryBusinessDTO> discoveryBusinessDTOS;
                    if (templateContentMap.containsKey(determineAssetData.getTemplateId())){
                        discoveryBusinessDTOS = templateContentMap.get(determineAssetData.getTemplateId());
                    } else {
                        discoveryBusinessDTOS =
                        discoveryTemplateContentMapper.queryDiscoveryBusinessDTO(determineAssetData.getTemplateId());
                        templateContentMap.put(determineAssetData.getTemplateId(), discoveryBusinessDTOS);
                    }
                    String inventoryId = SnowFlake.getId();
                    CoInventory coInventory = CoInventory.builder().operationId(modelParam.getOperationId())
                    .inventoryId(inventoryId).labelId(LabelEnum.ZCPD.getCode())
                    .dataAsset(determineAssetData.getName()).assetComment(determineAssetData.getIntroduce())
                    .systemId(determineAssetData.getSystemId()).sensitiveLevel(determineAssetData.getSensitiveLevel())
                    .sensitive(DcasUtil.getMatrix(determineAssetData.getSensitiveLevel(), 5))
                    .busSystem(busSystemMap.get(determineAssetData.getSystemId()))
                    .dataTag(DcasUtil.getDataTag(determineAssetData.getSensitiveLevel()))
                    .schemaName(determineAssetData.getBelongs()).build();
                    coInventoryList.add(coInventory);

                    // 同一资产对应多个业务类型
                    Map<Integer, List<DiscoveryBusinessDTO>> bizMap = discoveryBusinessDTOS.stream().collect(Collectors.groupingBy(DiscoveryBusinessDTO::getTypeId));
                    if (bizMap.containsKey(determineAssetData.getTypeId())) {
                        List<DiscoveryBusinessDTO> discoveryBusinessDTOS1 = bizMap.get(determineAssetData.getTypeId());
                        discoveryBusinessDTOS1.forEach(discoveryBusinessDTO -> {
                            DiscoveryClassifyData discoveryClassifyData =
                                DiscoveryClassifyData.builder().operationId(modelParam.getOperationId()).jobId(-1L)
                                    .templateId(determineAssetData.getTemplateId())
                                    .columnName("")
                                    .dataId(discoveryBusinessDTO.getDataId()).typeId(discoveryBusinessDTO.getTypeId())
                                    .sensitiveLevel(discoveryBusinessDTO.getSensitiveLevel())
                                    .belongs(discoveryBusinessDTO.getBelongs())
                                    .typeName(discoveryBusinessDTO.getTypeName()).bizId(discoveryBusinessDTO.getBizId())
                                    .sensitive(discoveryBusinessDTO.getSensitive())
                                    .assetName(discoveryBusinessDTO.getAssetName()).inventoryId(inventoryId).build();
                            discoveryClassifyDataList.add(discoveryClassifyData);
                        });
                    } else {
                        log.warn("key {} not exists", determineAssetData.getTypeId());
                    }
                }
            );
            coInventoryService.saveBatch(coInventoryList);
            PartitionUtils.part(discoveryClassifyDataList, discoveryClassifyDataMapper::insertList);
        }

        // 保存风险模型计算中间表数据
        //1、资产数据结果采集表
        updateMidOperation(modelParam);
        log.info("更新资产数据结果采集表成功!");

        //2、保存资产数据结果采集表
        saveBatchMidInventoryResult(modelParam.getOperationId(), coInventoryList);
        log.info("保存资产数据结果采集表成功!");

        //3、保存脆弱性数据结果采集表
        saveBatchMidVulResult(modelParam.getOperationId());
        log.info("保存脆弱性数据结果采集表!");
        operationModelMapper.insert(operationModel);
        return true;
    }

    private void saveBatchMidVulResult(String operationId) {
        iMidVulResultService.remove(new QueryWrapper<MidVulResult>().eq("operation_id", operationId));
        List<MidVulResult> midVulResults = new ArrayList<>();
        List<CoVerification> coVerifications =
            coVerificationService.list(new QueryWrapper<CoVerification>().eq("operation_id", operationId));
        coVerifications.forEach(coVerification -> {
            MidVulResult midVulResult =
                MidVulResult.builder()
                    .operationId(operationId)
                    .bpCode(coVerification.getBpCode())
                    .dimension(coVerification.getGpDimension())
                    .processTag(coVerification.getStage())
                    .regulatoryFactor(coVerification.getRegulatoryFactor())
                    .standardId(coVerification.getStandardId())
                    .systemId(coVerification.getSystemId())
                    .result(coVerification.getResult())
                    .itemRate(coVerification.getItemRate())
                    .build();
            midVulResults.add(midVulResult);
        });
        iMidVulResultService.saveBatch(midVulResults);
    }

    private void saveBatchMidInventoryResult(String operationId, List<CoInventory> coInventoryList) {
        // 先删除
        iMidInventoryResultService.remove(new QueryWrapper<MidInventoryResult>().eq("operation_id", operationId));
        List<MidInventoryResult> midInventoryResults = new ArrayList<>();
        if (CollUtil.isEmpty(coInventoryList)) {
            coInventoryList = coInventoryService.list(new QueryWrapper<CoInventory>().eq("operation_id", operationId));
        }
        CoOperation coOperation = coOperationMapper.selectById(operationId);
        List<DiscoveryClassifyData> discoveryClassifyDataList = discoveryClassifyDataMapper.selectList(
            new QueryWrapper<DiscoveryClassifyData>().eq("operation_id", operationId));
        Map<String, List<DiscoveryClassifyData>> discoveryClassifyDataMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(discoveryClassifyDataList)){
            discoveryClassifyDataMap = discoveryClassifyDataList.stream().collect(Collectors.groupingBy(DiscoveryClassifyData::getInventoryId));
        }
        Map<String, List<DiscoveryClassifyData>> finalDiscoveryClassifyDataMap = discoveryClassifyDataMap;
        Map<String, String> impactsMap = safetyImpactsClassificationCacheHolder.getSafetyImpactsFromCache();
        // 查询指定标准文件ID对应的stage字段值，此处固定 《信息安全技术 个人信息安全影响评估指南》
        List<String> stages = standardItemMapper.selectStagesByStandardId(1024);
        coInventoryList.forEach(coInventory -> {
            DbConfig dbConfig = null;
            if (coInventory.getDbConfig() != null) {
                dbConfig = JSONUtil.toBean(coInventory.getDbConfig().toString(), DbConfig.class);
            }
            Set<String> sensitiveColumnNames = new HashSet<>();
            Set<String> discoveryColumnNames = new HashSet<>();
            Set<String> classifySet = new HashSet<>();
            // 个人信息专项
            if (coOperation.getSpecId() != null && coOperation.getSpecId() == 2){
                for (String stage : stages){
                    for (PersonalSafetyImpactTypeEnum impactTypeEnum : PersonalSafetyImpactTypeEnum.values()) {
                        // 过滤资产发现出来的敏感字段
                        if (finalDiscoveryClassifyDataMap.containsKey(coInventory.getInventoryId())){
                            List<DiscoveryClassifyData> classifyDataList = finalDiscoveryClassifyDataMap.get(coInventory.getInventoryId());
                            for (int i=0; i<classifyDataList.size(); i++){
                                DiscoveryClassifyData discoveryClassifyData = classifyDataList.get(i);
                                String impact = impactsMap.get(discoveryClassifyData.getAssetName());
                                if (impactTypeEnum.getName().equals(impact)) {
                                    sensitiveColumnNames.add(discoveryClassifyData.getColumnName());
                                    discoveryColumnNames.add(discoveryClassifyData.getAssetName());
                                    if (discoveryClassifyData.getTypeName() != null) {
                                        classifySet.add(discoveryClassifyData.getTypeName());
                                    }
                                }
                            }
                        }
                        MidInventoryResult midInventoryResult =
                            MidInventoryResult.builder()
                                .operationId(operationId)
                                .tableName(coInventory.getDataAsset())
                                .schema(coInventory.getSchemaName())
                                .systemId(coInventory.getSystemId())
                                .sensitiveLevel(coInventory.getSensitiveLevel())
                                .systemId(coInventory.getSystemId())
                                .dbName(dbConfig != null ? dbConfig.getDbName() : null)
                                .host(dbConfig != null ? dbConfig.getHost() : null)
                                .port(dbConfig != null ? dbConfig.getPort() : null)
                                .sensitiveLevel(coInventory.getSensitiveLevel())
                                .dbType(dbConfig != null ? Integer.parseInt(dbConfig.getConfigType()) : null)
                                .dataTag(coInventory.getDataTag())
                                .sensitiveColumnName(sensitiveColumnNames.toArray(new String[0]))
                                .discoveryColumnName(discoveryColumnNames.toArray(new String[0]))
                                .riskSourceDimension(stage)
                                .safetyImpact(impactTypeEnum.getName())
                                .classify(String.join(StrPool.COMMA, classifySet))
                                .build();
                        midInventoryResults.add(midInventoryResult);
                    }
                }
            } else {
                if (finalDiscoveryClassifyDataMap.containsKey(coInventory.getInventoryId())){
                    List<DiscoveryClassifyData> classifyDataList = finalDiscoveryClassifyDataMap.get(coInventory.getInventoryId());
                    for (DiscoveryClassifyData discoveryClassifyData : classifyDataList) {
                        sensitiveColumnNames.add(discoveryClassifyData.getColumnName());
                        discoveryColumnNames.add(discoveryClassifyData.getAssetName());
                        if (discoveryClassifyData.getTypeName() != null) {
                            classifySet.add(discoveryClassifyData.getTypeName());
                        }
                    }
                }
                MidInventoryResult midInventoryResult =
                    MidInventoryResult.builder()
                        .operationId(operationId)
                        .tableName(coInventory.getDataAsset())
                        .schema(coInventory.getSchemaName())
                        .systemId(coInventory.getSystemId())
                        .sensitiveLevel(coInventory.getSensitiveLevel())
                        .systemId(coInventory.getSystemId())
                        .dbName(dbConfig != null ? dbConfig.getDbName() : null)
                        .host(dbConfig != null ? dbConfig.getHost() : null)
                        .port(dbConfig != null ? dbConfig.getPort() : null)
                        .sensitiveLevel(coInventory.getSensitiveLevel())
                        .dbType(dbConfig != null ? Integer.parseInt(dbConfig.getConfigType()) : null)
                        .dataTag(coInventory.getDataTag())
                        .sensitiveColumnName(sensitiveColumnNames.toArray(new String[0]))
                        .discoveryColumnName(discoveryColumnNames.toArray(new String[0]))
                        .classify(String.join(StrPool.COMMA, classifySet))
                        .build();
                midInventoryResults.add(midInventoryResult);
            }
        });
        iMidInventoryResultService.saveBatch(midInventoryResults);
    }

    private void updateMidOperation(ModelParam modelParam) {
        ModelConfig modelConfig = modelConfigMapper.selectById(modelParam.getModelId());
        Integer maxThreatLevel = threatLevelMapper.selectMaxLevel(modelConfig.getThreatLibraryId());
        CoConstant coConstant = coConstantMapper.queryByOperationId(modelParam.getOperationId());
        List<MidOperation> midOperationList = iMidOperationService.list(new QueryWrapper<MidOperation>().eq("operation_id", modelParam.getOperationId()));
        midOperationList.forEach(midOperation -> {
            midOperation.setMaxVul(modelConfig.getMaxVul());
            midOperation.setModelId(modelConfig.getId());
            midOperation.setMaxThreatLevel(maxThreatLevel);
            midOperation.setThreatTemplateId(modelConfig.getThreatLibraryId());
            midOperation.setAnalysisTemplateId(modelConfig.getAnalysisId());
            midOperation.setHighestSensitiveLevel(coConstant.getHighestSensitiveLevel());
        });
        iMidOperationService.updateBatchById(midOperationList);
    }

    @DataValidator(type = TemplateTypeEnum.RISK_TEMPLATE)
    @Override
    @SchemaSwitch(value = String.class)
    public List<SelectedKeyValueVO<Long, String>> listTemplate(String operationId) {
        List<SelectedKeyValueVO<Long, String>> list = new ArrayList<>();

        List<ModelConfig> modelConfigs = modelConfigMapper.selectList(new QueryWrapper<ModelConfig>().eq("enable", true));
        if (modelConfigs == null){
            return list;
        }

        Long templateId = coVerificationService.queryApplyTemplate(operationId);
        OperationModel operationModel = operationModelMapper.selectOne(new QueryWrapper<OperationModel>().eq("operation_id", operationId));
        modelConfigs.forEach(modelConfig -> {

            if (!modelConfig.getAnalysisId().equals(templateId)){
                return;
            }

            SelectedKeyValueVO<Long, String> keyValue = new SelectedKeyValueVO<>();
            keyValue.setKey(modelConfig.getId());
            keyValue.setValue(modelConfig.getName());
            if (operationModel != null && operationModel.getModelId().equals(modelConfig.getId())){
                keyValue.setIsSelected(true);
            } else {
                keyValue.setIsSelected(false);
            }
            keyValue.setUnderstand(modelConfig.getIsDefault());
            if (modelConfig.getThreatLibraryId() != null && modelConfig.getImpactLibraryId() != null){
                keyValue.setReference(Arrays.toString(LibraryTypeEnum.values()));
            } else if (modelConfig.getThreatLibraryId() != null) {
                keyValue.setReference(LibraryTypeEnum.THREAT.getName());
                ThreatTemplate threatTemplate = threatTemplateMapper.selectById(modelConfig.getThreatLibraryId());
                keyValue.setHasChild(threatTemplate.getChildFlag());
            } else if (modelConfig.getImpactLibraryId() != null) {
                keyValue.setReference(LibraryTypeEnum.IMPACT.getName());
            }
            list.add(keyValue);
        });
        return list;
    }

    @Override
    public RiskAnalysisResultVO getRiskAnalysisResult(String operationId, Integer pageNum, Integer pageSize,
        String searchType, String searchContent) {
        OperationModel operationModel = operationModelMapper.selectOne(new QueryWrapper<OperationModel>().eq("operation_id", operationId));
        if (operationModel == null){
            return null;
        }
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));
        RiskAnalysisResultVO riskAnalysisResultVO = new RiskAnalysisResultVO();
        if (CollUtil.isEmpty(coModelAnalysisResultList)){
            return riskAnalysisResultVO;
        }
        List<Map<String, String>> result = new ArrayList<>();
        AtomicReference<List<FormConfigTreeVO>> titles = new AtomicReference<>(new ArrayList<>());
        coModelAnalysisResultList.forEach(coModelAnalysisResult -> {
            if (coModelAnalysisResult == null || coModelAnalysisResult.getStatus() == 0){
                return;
            }
            titles.set(JSONUtil.toList(coModelAnalysisResult.getTitles(), FormConfigTreeVO.class));
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            mapList.forEach(map -> {
                if ("busSystem".equals(searchType)){
                    String res = (String)map.get("0");
                   if (res != null && res.equals(searchContent)){
                       result.add(map);
                   }
                } else if ("assertsName".equals(searchType)){
                    String res = (String)map.get("200");
                    if (res != null && res.equals(searchContent)){
                        result.add(map);
                    }
                } else if ("assertsLevel".equals(searchType)){
                    String res = (String)map.get("201");
                    if (res != null && res.equals(searchContent)){
                        result.add(map);
                    }
                } else {
                    result.add(map);
                }
            });
        });

        //内存分页
        List<Map<String, String>> pageList = ListUtil.page(pageNum-1, pageSize, result);
        riskAnalysisResultVO.setResultList(pageList);
        riskAnalysisResultVO.setTotal(result.size());
        riskAnalysisResultVO.setTitles(titles.get());
        return riskAnalysisResultVO;
    }

    @Override
    public void update(OperationUpdateDTO dto) {
        CoOperation coOperation = coOperationMapper.selectById(dto.getOperationId());
        if (Objects.isNull(coOperation)) {
            throw new ServiceException("作业不存在");
        }
        CoOperation bean = BeanUtil.copyProperties(dto, CoOperation.class);
        coOperationMapper.updateById(bean);
    }

    @Override
    public void updateQuestionnaire(CommonDto dto) {
        QueryWrapper<CoProgress> deleteWrapper = new QueryWrapper<>();
        String operationId = dto.getOperationId();
        Long labelId = dto.getLabelId();
        deleteWrapper.eq("operation_id", operationId);
        deleteWrapper.eq("label_id", labelId);
        coProgressMapper.delete(deleteWrapper);

        updateProcess(operationId);
    }

    private void updateProcess(String operationId) {
        //更新过程进度
        QueryWrapper<CoProgress> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        //已完成模块
        int completedNum = coProgressMapper.selectCount(queryWrapper);

        //查询作业服务内容，获取子树数量
        double allNum = selectAllLabelNum(operationId);
        //进度=已完成模块/总模块*100% 进度四舍五入保留两位小数
        double progress = Arith.round(completedNum / allNum * 100, 2);
        UpdateWrapper<CoProgress> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("operation_id", operationId);
        updateWrapper.set("progress", BigDecimal.valueOf(progress));
        //更新进度表
        coProgressMapper.update(new CoProgress(), updateWrapper);
        //更新作业表
        CoOperation operation = new CoOperation();
        operation.setOperationId(operationId);
        operation.setProgress(BigDecimal.valueOf(progress));
        coOperationMapper.updateById(operation);
    }

    @DataValidator(type = TemplateTypeEnum.SPEC_TEMPLATE)
    @Override
    @SchemaSwitch
    public List<SpecVO> getSpecList(TemplateQueryDTO dto) {
        try {
            //查询有效专项评估列表
            List<SpecialEvaluationConfig> list = specialEvaluationConfigMapper.selectList(
                new QueryWrapper<SpecialEvaluationConfig>().eq("enable", true));
            if (CollUtil.isEmpty(list)) {
                return Collections.emptyList();
            }
            final Map<String, String> industryMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", 2)
                    .eq("status", 0)).stream().collect(Collectors.toMap(t -> t.getId().toString(), Tag::getName));
            List<String> industryList = StrUtil.split(dto.getIndustry(), StrUtil.COMMA);
            List<SpecVO> specList = new ArrayList<>();
            for (SpecialEvaluationConfig specialEvaluationConfig : list) {
                Template template = templateMapper.selectById(Long.valueOf(specialEvaluationConfig.getTemplateId()));
                boolean filter = templateFilter(dto.getRegion(), industryList, StrUtil.EMPTY,
                        specialEvaluationConfig.getRegionCode(), specialEvaluationConfig.getIndustryCode(), industryMap);
                if (!filter)
                    continue;
                SpecVO specVO = new SpecVO();
                specVO.setId(specialEvaluationConfig.getId());
                specVO.setName(specialEvaluationConfig.getName());
                specVO.setServiceContent(specialEvaluationConfig.getEvaluationContent());
                specVO.setTemplateId(specialEvaluationConfig.getTemplateId());
                if (Objects.nonNull(template))
                    specVO.setTemplateName(template.getName());
                specList.add(specVO);
            }
            return specList;
        } catch (Exception e){
          log.error("获取专项评估模板列表报错", e);
            return Collections.emptyList();
        }
    }
}
