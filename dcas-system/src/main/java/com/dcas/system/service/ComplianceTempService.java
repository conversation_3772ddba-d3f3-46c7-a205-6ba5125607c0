package com.dcas.system.service;

import com.dcas.common.utils.PageResult;
import com.dcas.common.domain.entity.ComplianceTemplate;
import com.dcas.common.model.param.ComplianceTemParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.TemplateSearchReq;
import com.dcas.common.model.vo.ComplianceTemVO;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/2 17:08
 * @since 1.2.0
 */
public interface ComplianceTempService {

    void addCompliance(ComplianceTemParam req);

    PageResult<ComplianceTemplate> list(Integer currentPage, Integer pageSize);

    void updateCompliance(ComplianceTemParam param);

    void deleteCompliance(IdsReq req);

    void setDefault(Integer templateId);

    void enableCompliance(Integer templateId);

    void createBak(ComplianceTemParam req);

    ComplianceTemVO details(Integer templateId);

    List<ComplianceTemplate> search(TemplateSearchReq req);
}
