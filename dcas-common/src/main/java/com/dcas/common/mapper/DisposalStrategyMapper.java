package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.model.dto.DisposalSearchDTO;
import com.dcas.common.domain.entity.AdviseScheme;
import com.dcas.common.domain.entity.DisposalStrategy;
import com.dcas.common.model.vo.DisposalStrategyVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/4/18 16:18
 * @since 1.3.0
 */
public interface DisposalStrategyMapper extends BaseMapper<DisposalStrategy> {

    List<DisposalStrategyVO> qryStrategies();

    DisposalStrategyVO qryById(@Param("id") Integer id);

    List<DisposalStrategyVO> search(@Param("dto") DisposalSearchDTO dto);

    List<AdviseScheme> selectSchemeByItemIdsForLegal(@Param("itemIds") Collection<String> itemIds, @Param("lawIds") Collection<Integer> lawIds);

    List<AdviseScheme> selectSchemeByItemIdsForCapacity(@Param("itemIds") Collection<String> itemIds, @Param("standardIds") Collection<Integer> standardIds);
}
