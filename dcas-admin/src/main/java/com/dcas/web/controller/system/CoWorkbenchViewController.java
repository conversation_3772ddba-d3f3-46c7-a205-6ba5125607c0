package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.BaseEntity;
import com.dcas.common.core.domain.R;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.model.dto.QueryOperationOnGoingViewDto;
import com.dcas.common.model.vo.QueryOperationOnGoingView;
import com.dcas.common.model.vo.QueryOperationStatusVo;
import com.dcas.system.service.CoWorkbenchViewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * fetch data
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/workbench")
@Api(tags = "工作台")
public class CoWorkbenchViewController {

    private final CoWorkbenchViewService coWorkbenchViewService;

    /**
     * 查询作业状态
     *
     */
    @ApiOperation(value = "查询作业状态")
    @GetMapping(value = "/view/retrieveStatus")
    public R<QueryOperationStatusVo> retrieveStatus() {
        QueryOperationOnGoingViewDto dto = new QueryOperationOnGoingViewDto();
        return R.success(coWorkbenchViewService.retrieveStatus(dto));
    }

    /**
     * 执行中作业列表
     */
    @ApiOperation(value = "执行中作业列表")
    @PostMapping(value = "/view/retrieveOperation")
    public R<List<QueryOperationOnGoingView>> selectOperation(@RequestBody RequestModel<QueryOperationOnGoingViewDto> dto) {
        return R.success(coWorkbenchViewService.selectOperation(dto));
    }

    /**
     * 复核待办作业列表
     */
    @ApiOperation(value = "复核待办作业列表")
    @PostMapping(value = "/view/retrieveWaitReview")
    public R<List<QueryOperationOnGoingView>> retrieveWaitReview(@RequestBody RequestModel<QueryOperationOnGoingViewDto> dto) {
        if (Objects.isNull(dto) || dto.getPrivator() == null){
            dto.setPrivator(new QueryOperationOnGoingViewDto());
        }

        return R.success(coWorkbenchViewService.retrieveWaitReview(dto));
    }

    /**
     * 作业分布图表
     */
    @ApiOperation(value = "作业分布图表")
    @PostMapping(value = "/view/retrieveRelated")
    public R<Map<String, Map<String, Double>>> retrieveRelated() {
        return R.success(coWorkbenchViewService.retrieveRelated(new BaseEntity()));
    }
}
