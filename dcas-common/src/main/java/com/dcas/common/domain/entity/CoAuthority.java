package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dcas.common.annotation.Excel;
import com.dcas.common.model.other.PgJSONTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据权限业务表
 */
@Data
@ApiModel("数据权限")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("co_authority")
public class CoAuthority implements Serializable {
    /**
     * 数据权限id
     */
    @ApiModelProperty(value = "数据权限id")
    @TableId(type = IdType.ASSIGN_ID)
    private String authorityId;

    /**
     * 作业id
     */
    private String operationId;

    /**
     * 分类id
     */
    private Long labelId;

    /**
     * 模式名
     */
    private String schemaName;

    /**
     * 数据资产（表）
     */
    @Excel(name = "数据资产（表）")
    private String dataAsset;

    /**
     * 用户名
     */
    private String username;

    /**
     * 数据库类型
     */
    @Excel(name = "数据库类型")
    private String dbType;

    /**
     * 所属业务系统
     */
    @Excel(name = "所属业务系统")
    private String busSystem;

    /**
     * 是否拥有新增表权限
     */
    @Excel(name = "拥有新增权限的账户")
    private String insertTable;

    /**
     * 是否拥有删除表数据权限
     */
    @Excel(name = "拥有删除表数据权限的账户")
    private String deleteTable;

    /**
     * 是否拥有删除表权限
     */
    @Excel(name = "拥有删除表权限的账户")
    private String dropTable;

    /**
     * 是否拥有修改表权限
     */
    @Excel(name = "拥有修改权限的账户")
    private String updateTable;

    /**
     * 是否拥有查询表权限
     */
    @Excel(name = "拥有查询权限的账户")
    private String selectTable;

    /**
     * 是否拥有全部权限（增删改查）
     */
    @Excel(name = "拥有全部权限的账户")
    private String crudTable;

    @TableField(typeHandler = PgJSONTypeHandler.class)
    private Object dbConfig;

    /**
     * 权限字符
     */
    private String pri;

    /**
     * 类型（1=数据库所有表；2=数据库用户权限表）
     */
    private Integer type;

    /**
     * 权限查询作业id co_db_security 主键
     */
    private Long jobId;

    private static final long serialVersionUID = 1L;

    public boolean hasInsertPermission() {
        return "1".equals(insertTable);
    }

    public boolean hasUpdatePermission() {
        return "1".equals(updateTable);
    }

    public boolean hasDeletePermission() {
        return "1".equals(deleteTable);
    }

    public boolean hasDropPermission() {
        return "1".equals(dropTable);
    }

    public boolean hasSelectPermission() {
        return "1".equals(selectTable);
    }

    public boolean hasCrudPermission() {
        return hasInsertPermission() && hasUpdatePermission() && hasDeletePermission() && hasDropPermission() && hasSelectPermission();
    }
}