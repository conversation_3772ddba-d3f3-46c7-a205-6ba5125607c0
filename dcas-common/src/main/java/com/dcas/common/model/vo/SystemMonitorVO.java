package com.dcas.common.model.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 系统监控VO
 *
 * <AUTHOR>
 * @date 2023/12/25 16:41
 **/
@Getter
@Setter
@Builder
@ToString
public class SystemMonitorVO {

    @ApiModelProperty(value = "cpu使用率")
    private String cpuUsedRate;

    @ApiModelProperty(value = "内存使用率")
    private String memoryUsedRate;

    @ApiModelProperty(value = "网络上传速率kb/s")
    private String networkUpSpeed;

    @ApiModelProperty(value = "网络下载速率kb/s")
    private String networkDownSpeed;

    @ApiModelProperty(value = "磁盘读速率kb/s")
    private String diskReadSpeed;

    @ApiModelProperty(value = "磁盘写速率kb/s")
    private String diskWriteSpeed;
}
