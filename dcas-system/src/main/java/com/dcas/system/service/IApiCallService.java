package com.dcas.system.service;

import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.domain.entity.TaskStep;
import com.dcas.common.model.dto.StepMsgDTO;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.IntegrationInterfaceVO;
import lombok.Data;

import java.util.List;

/**
 * API调用Service接口（占位符接口，后续实现具体的API集成）
 * 
 * <AUTHOR>
 */
public interface IApiCallService {
    
    /**
     * 执行API调用
     *
     * @param taskStep 任务步骤配置
     * @param params 接口参数
     * @return API调用结果
     */
    ApiCallResult executeApiCall(TaskStep taskStep, List<IntegrationFormFieldVO> params);

    /**
     * 测试API连接
     *
     * @param apiInterface api接口
     * @param configParams 配置参数
     * @param url 基础URL
     * @return 连接测试结果
     */
    StepMsgDTO testApiConnection(ApiInterface apiInterface, List<IntegrationFormFieldVO> configParams, String url);

    /**
     * 测试API连接（带认证配置）
     *
     * @param apiInterface api接口
     * @param configParams 配置参数
     * @param url 基础URL
     * @param authConfig 认证配置JSON
     * @return 连接测试结果
     */
    StepMsgDTO testApiConnection(ApiInterface apiInterface, List<IntegrationFormFieldVO> configParams, String url, String authConfig);

    /**
     * 验证API配置
     *
     * @param taskStep 任务步骤配置
     * @return 验证结果
     */
    boolean validateApiConfig(TaskStep taskStep);

    /**
     * 解析响应数据
     *
     * @param responseData 响应数据
     * @param taskStep 任务步骤配置
     * @return 解析后的输出数据
     */
    String parseResponseData(String responseData, TaskStep taskStep);

    /**
     * 检查响应是否成功
     *
     * @param responseStatus 响应状态码
     * @param responseData 响应数据
     * @return 是否成功
     */
    boolean isResponseSuccessful(Integer responseStatus, String responseData);

    /**
     * API调用结果内部类
     */
    @Data
    class ApiCallResult {
        private boolean success;
        private Integer statusCode;
        private String responseData;
        private String outputData;
        private String errorMessage;
        private long executionTime;

        public ApiCallResult(boolean success, Integer statusCode, String responseData, String outputData, String errorMessage, long executionTime) {
            this.success = success;
            this.statusCode = statusCode;
            this.responseData = responseData;
            this.outputData = outputData;
            this.errorMessage = errorMessage;
            this.executionTime = executionTime;
        }

        /**
         * 创建成功结果
         */
        public static ApiCallResult success(Integer statusCode, String responseData, String outputData, long executionTime) {
            return new ApiCallResult(true, statusCode, responseData, outputData, null, executionTime);
        }

        /**
         * 创建失败结果
         */
        public static ApiCallResult failure(Integer statusCode, String responseData, String errorMessage, long executionTime) {
            return new ApiCallResult(false, statusCode, responseData, null, errorMessage, executionTime);
        }

        /**
         * 创建异常结果
         */
        public static ApiCallResult exception(String errorMessage, long executionTime) {
            return new ApiCallResult(false, null, null, null, errorMessage, executionTime);
        }
    }
}
