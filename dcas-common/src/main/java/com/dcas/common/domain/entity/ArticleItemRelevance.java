package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/6 11:31
 * @since 1.2.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("article_item_relevance")
public class ArticleItemRelevance {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String articleId;

    private String itemId;

    private String matchTags;
}
