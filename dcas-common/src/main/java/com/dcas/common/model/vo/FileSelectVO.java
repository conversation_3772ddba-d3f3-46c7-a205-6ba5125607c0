package com.dcas.common.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class FileSelectVO extends SelectItemVO<String, String> {
    private Integer type;

    public FileSelectVO(String name, String value, Integer type) {
        super(name, value);
        this.type = type;
    }
}
