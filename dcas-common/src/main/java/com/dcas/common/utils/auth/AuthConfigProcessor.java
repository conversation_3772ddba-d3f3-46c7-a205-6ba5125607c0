package com.dcas.common.utils.auth;

import cn.hutool.core.util.StrUtil;
import com.dcas.common.enums.AuthConstants;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.dto.AuthProcessResult;
import com.dcas.common.model.param.AlgorithmParameter;
import com.dcas.common.model.param.AuthParameter;
import com.dcas.common.model.param.SystemAutoParameter;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.params.AuthParameterParser;
import com.dcas.common.utils.sign.Md5Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 认证配置处理器
 * 处理authConfig JSON配置，生成最终的认证参数
 *
 * <AUTHOR>
 * @since 2.1.3.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthConfigProcessor {
    
    private final AuthParameterParser authParameterParser;
    
    /**
     * 处理认证配置，生成最终的认证参数
     *
     * @param authConfig 认证配置JSON字符串
     * @param userInputParams 用户输入的参数列表
     * @return 处理结果，包含头部参数和体部参数
     */
    public AuthProcessResult processAuthConfig(String authConfig, List<IntegrationFormFieldVO> userInputParams) {
        try {
            if (StrUtil.isBlank(authConfig)) {
                log.debug("认证配置为空，返回空参数");
                return AuthProcessResult.success(new HashMap<>(), new HashMap<>());
            }
            
            // 解析认证配置
            List<AuthParameter> authParameters = authParameterParser.parseAuthParameters(authConfig);
            if (authParameters.isEmpty()) {
                log.debug("认证配置解析结果为空，返回空参数");
                return AuthProcessResult.success(new HashMap<>(), new HashMap<>());
            }
            
            // 构建用户输入参数映射，便于查找
            Map<String, String> userInputMap = buildUserInputMap(userInputParams);
            
            // 处理各类型参数
            Map<String, String> headerParams = new HashMap<>();
            Map<String, String> bodyParams = new HashMap<>();
            
            for (AuthParameter authParam : authParameters) {
                String paramValue = generateParameterValue(authParam, userInputMap);
                if (paramValue != null) {
                    if (Boolean.TRUE.equals(authParam.getInHeader())) {
                        headerParams.put(authParam.getKey(), paramValue);
                    } else {
                        bodyParams.put(authParam.getKey(), paramValue);
                    }
                }
            }
            
            log.info("认证参数处理完成，头部参数{}个，体部参数{}个", headerParams.size(), bodyParams.size());
            return AuthProcessResult.success(headerParams, bodyParams);
            
        } catch (Exception e) {
            log.error("处理认证配置失败: {}", e.getMessage(), e);
            return AuthProcessResult.failure("处理认证配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建用户输入参数映射
     */
    private Map<String, String> buildUserInputMap(List<IntegrationFormFieldVO> userInputParams) {
        Map<String, String> userInputMap = new HashMap<>();
        if (userInputParams != null) {
            for (IntegrationFormFieldVO param : userInputParams) {
                if (StrUtil.isNotBlank(param.getName()) && StrUtil.isNotBlank(param.getValue())) {
                    // 解密参数值
                    String decryptedValue = SecurityUtils.decryptAes(param.getValue());
                    userInputMap.put(param.getName(), decryptedValue);
                }
            }
        }
        return userInputMap;
    }
    
    /**
     * 根据参数类型生成参数值
     */
    private String generateParameterValue(AuthParameter authParam, Map<String, String> userInputMap) {
        String paramType = authParam.getType();
        
        switch (paramType) {
            case "USER_INPUT":
                return handleUserInputParameter(authParam, userInputMap);
                
            case "SYSTEM_AUTO":
                return handleSystemAutoParameter((SystemAutoParameter) authParam);
                
            case "ALGORITHM":
                return handleAlgorithmParameter((AlgorithmParameter) authParam, userInputMap);
                
            default:
                log.warn("未知的参数类型: {}", paramType);
                return null;
        }
    }
    
    /**
     * 处理用户输入类型参数
     */
    private String handleUserInputParameter(AuthParameter authParam, Map<String, String> userInputMap) {
        String value = userInputMap.get(authParam.getKey());
        if (StrUtil.isBlank(value)) {
            log.warn("用户输入参数 {} 的值为空", authParam.getKey());
        }
        return value;
    }
    
    /**
     * 处理系统自动生成类型参数
     */
    private String handleSystemAutoParameter(SystemAutoParameter systemParam) {
        String autoType = systemParam.getAutoType();
        
        switch (autoType) {
            case "TIMESTAMP_MILLIS":
                return String.valueOf(System.currentTimeMillis());
                
            case "TIMESTAMP_SECONDS":
                return String.valueOf(System.currentTimeMillis() / 1000);
                
            case "UUID":
                return UUID.randomUUID().toString().replace("-", "");
                
            case "RANDOM_STRING":
                return generateRandomString(16); // 默认16位随机字符串
                
            default:
                log.warn("未支持的系统自动生成类型: {}", autoType);
                return null;
        }
    }
    
    /**
     * 处理算法计算类型参数
     */
    private String handleAlgorithmParameter(AlgorithmParameter algorithmParam, Map<String, String> userInputMap) {
        String formula = algorithmParam.getFormula();
        String algorithmType = algorithmParam.getAlgorithmType();
        
        if (StrUtil.isBlank(formula)) {
            log.warn("算法参数 {} 的公式为空", algorithmParam.getKey());
            return null;
        }
        
        // 替换公式中的占位符
        String processedFormula = replaceFormulaPlaceholders(formula, userInputMap);
        
        // 根据算法类型计算结果
        switch (algorithmType) {
            case "MD5":
                return Md5Utils.hash(processedFormula);
                
            case "SHA256":
                return calculateSha256(processedFormula);
                
            case "BASE64":
                return Base64.getEncoder().encodeToString(processedFormula.getBytes());
                
            default:
                log.warn("未支持的算法类型: {}", algorithmType);
                return null;
        }
    }
    
    /**
     * 替换公式中的占位符
     * 例如: "{access-time}-{access-key-secret}" -> "1625097600000-mySecretKey"
     */
    private String replaceFormulaPlaceholders(String formula, Map<String, String> userInputMap) {
        Pattern pattern = Pattern.compile("\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(formula);
        
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String placeholder = matcher.group(1);
            String value = userInputMap.get(placeholder);
            
            if (value == null) {
                // 如果是系统自动生成的参数，尝试生成
                value = generateSystemValue(placeholder);
            }
            
            if (value == null) {
                log.warn("公式中的占位符 {} 无法找到对应的值", placeholder);
                value = "";
            }
            
            matcher.appendReplacement(result, Matcher.quoteReplacement(value));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 生成系统值（用于公式中的占位符）
     */
    private String generateSystemValue(String placeholder) {
        switch (placeholder) {
            case "access-time":
            case "timestamp":
                return String.valueOf(System.currentTimeMillis());
            case "uuid":
                return UUID.randomUUID().toString().replace("-", "");
            default:
                return null;
        }
    }
    
    /**
     * 计算SHA256哈希
     */
    private String calculateSha256(String input) {
        try {
            java.security.MessageDigest digest = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(java.nio.charset.StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            log.error("计算SHA256失败: {}", e.getMessage(), e);
            throw new ServiceException("计算SHA256失败", e);
        }
    }
    
    /**
     * 生成随机字符串
     */
    private String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }
}
