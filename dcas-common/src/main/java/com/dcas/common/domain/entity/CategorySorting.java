package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/28 16:41
 * @since 1.4.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("category_sorting")
public class CategorySorting {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer parentId;

    private Integer childId;

    private Integer sort;
}
