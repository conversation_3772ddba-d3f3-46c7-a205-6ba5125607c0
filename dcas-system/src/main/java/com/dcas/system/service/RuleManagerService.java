package com.dcas.system.service;

import com.github.pagehelper.PageInfo;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.model.dto.AddModelDto;
import com.dcas.common.model.dto.PrimaryKeyListDTO;
import com.dcas.common.model.dto.QueryModelDTO;
import com.dcas.common.model.dto.UpdateModelDTO;
import com.dcas.common.domain.entity.CoModel;

import java.text.ParseException;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/6/15 17:58
 * @ClassName RuleManagerService
 */
public interface RuleManagerService {
    /**
     * 新增合规模板
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:51
     */
    int add(RequestModel<AddModelDto> dto) throws ParseException;

    /**
     * 删除合规模板
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:39
     */
    int delete(RequestModel<PrimaryKeyListDTO> dto);

    /**
     * 更新合规模板
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:46
     */
    int update(RequestModel<UpdateModelDTO> dto);

}
