package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.domain.entity.ApiRelease;
import com.dcas.common.model.vo.*;

import java.util.List;

/**
 * API版本记录服务接口
 *
 * <AUTHOR>
 */
public interface IApiReleaseService extends IService<ApiRelease> {
    
    /**
     * 查询产品能力树形结构
     *
     * @return 产品能力树形结构列表
     */
    List<ProductCapabilityTreeVO> getProductCapabilityTree();

    List<IntegrationInterfaceVO> getProductsByReleaseId(Long productId, Integer taskType);

    List<ApiReleaseVO> queryApiReleaseList();

    List<IntegrationFormFieldVO> getProductHeader(Long releaseId);

    /**
     * 获取对接产品能力列表，供评估内容使用
     * @return {@link  List<AbilityModelVO>}
     */
    List<AbilityModelVO> getProductCapability();
}
