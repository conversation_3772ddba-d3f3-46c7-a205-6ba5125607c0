select add_column_to_tables('special_evaluation_config', 'introduce', 'varchar(255)');
select add_column_to_tables('special_evaluation_config', 'region_code', 'varchar(64)');
select add_column_to_tables('special_evaluation_config', 'industry_code', 'varchar(255)');
select add_column_to_tables('standard', 'assign', 'varchar(255)');

Do $$
BEGIN
BEGIN
alter table public.questionnaire add sort integer;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column sort already exists in questionnaire.';
END;
END;
$$;
comment on column public.questionnaire.sort is '问题排序';