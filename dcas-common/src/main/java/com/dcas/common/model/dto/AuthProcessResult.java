package com.dcas.common.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 认证参数处理结果
 *
 * <AUTHOR>
 * @since 2.1.3.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthProcessResult {
    
    /**
     * 需要放在请求头中的认证参数
     */
    private Map<String, String> headerParams;
    
    /**
     * 需要放在请求体或查询参数中的认证参数
     */
    private Map<String, String> bodyParams;
    
    /**
     * 是否处理成功
     */
    private boolean success;
    
    /**
     * 错误信息（处理失败时）
     */
    private String errorMessage;
    
    /**
     * 创建成功结果
     */
    public static AuthProcessResult success(Map<String, String> headerParams, Map<String, String> bodyParams) {
        return new AuthProcessResult(headerParams, bodyParams, true, null);
    }
    
    /**
     * 创建失败结果
     */
    public static AuthProcessResult failure(String errorMessage) {
        return new AuthProcessResult(null, null, false, errorMessage);
    }
}
