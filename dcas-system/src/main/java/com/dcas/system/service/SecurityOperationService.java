package com.dcas.system.service;

import com.dcas.common.exception.file.InvalidExtensionException;
import com.dcas.common.model.dto.TemplateQueryDTO;
import com.dcas.common.utils.PageResult;
import com.dcas.common.model.dto.SecurityContentDTO;
import com.dcas.common.model.dto.SecurityProduceDTO;
import com.dcas.common.domain.entity.SecurityTemplate;
import com.dcas.common.model.param.SecurityEditParam;
import com.dcas.common.model.param.SecurityWorkParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.SecurityReportReq;
import com.dcas.common.model.req.SecuritySearchReq;
import com.dcas.common.model.vo.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/3 11:08
 * @since 1.4.0
 */
public interface SecurityOperationService {

    /**
     * 创建安全作业
     *
     * @param param 安全作业参数
     */
    Integer create(SecurityWorkParam param);

    void produce(SecurityProduceDTO dto);

    /**
     * 分页查询安全作业
     *
     * @return 安全作业分页结果
     */
    PageResult<SecurityWorkVO> pageQuery(SecuritySearchReq req);

    /**
     * 编辑安全作业
     *
     * @param param 安全作业参数
     */
    void edit(SecurityEditParam param);

    /**
     * 删除安全作业
     *
     * @param req 安全作业id
     */
    void delete(IdsReq req);

    /**
     * 执行安全作业
     *
     * @param id 安全作业id
     * @return 安全作业标签列表
     */
    List<SecurityLabelVO> execute(Integer id);

    /**
     * 安全作业内容
     *
     * @param id 安全作业id
     * @param categoryName 检查类别名称
     * @return 安全作业内容
     */
    List<SecurityContentVO> content(Integer id, String categoryName);

    /**
     * 保存安全作业内容
     *
     * @param dto 安全作业内容
     */
    void saveContent(SecurityContentDTO dto);

    /**
     * 编辑安全作业进度
     *
     * @param dto 安全作业内容
     */
    void editProgress(SecurityContentDTO dto);

    List<SecurityTemplate> getSecurityTemplateList(TemplateQueryDTO dto);

    /**
     * 导出安全作业
     *
     * @param response    响应
     * @param req 请求参数
     */
    void exportWord(HttpServletResponse response, SecurityReportReq req);

    /**
     * 导出文件压缩包
     *
     * @param id 安全作业id
     */
    void fileDownload(Integer id, HttpServletResponse response);

    /**
     * 安全作业内容附件
     *
     * @param id 安全作业id
     * @param categoryName 检查类别名称
     */
    List<ContentFileVO> contentFile(Integer id, String categoryName);

    /**
     * 上传检查内容附件
     *
     * @param contentId 检查内容id
     * @param file  文件
     */
    Map<String, Object> uploadContentFile(Integer contentId, MultipartFile file) throws IOException, InvalidExtensionException;

    /**
     * 清理附件：删除表数据，删除服务器存储附件文件
     *
     */
    void deleteContentFile(Integer contentId, String fileId);

    /**
     * 安全作业结果
     *
     */
    SecurityCategoryResultVO result(Integer operationId, String categoryName);

    SecurityDetailVO detail(Integer id);

    void editStatus(Integer securityId);

    /**
     * 删除安全作业
     *
     * @param projectIds 项目ids
     */
    void deleteByProjectIds(List<String> projectIds);

    void flushBasic(Integer id);

    List<Map<Integer, String>> listServiceContent();

    Object pdfDownload(Integer templateId, HttpServletRequest request);
}
