package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * API版本记录实体类
 * 包含产品类型、产品所属公司、产品logo以及产品对接参数（token）等
 *
 * <AUTHOR>
 */
@Data
@ApiModel("API版本记录")
@TableName("api_release")
public class ApiRelease implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 产品类型名称
     */
    @ApiModelProperty("产品类型名称")
    private String name;
    
    /**
     * 对接产品能力
     */
    @ApiModelProperty("对接产品能力ID")
    private String capability;

    @TableField(exist = false)
    @ApiModelProperty("对接产品能力名称")
    private String capabilityName;
    
    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司")
    private String company;
    
    /**
     * 产品logo
     */
    @ApiModelProperty("产品logo")
    private String logo;
    
    /**
     * 产品授权字段参数
     */
    @ApiModelProperty("产品授权字段参数")
    private String auth;

    /**
     * 产品授权参数配置规则
     */
    @ApiModelProperty("产品授权参数配置规则")
    private String authConfig;
    
    /**
     * 能力标签类型
     */
    @ApiModelProperty("能力标签类型")
    private String ability;
    
    /**
     * 应用标签
     */
    @ApiModelProperty("应用标签")
    private String appTag;
    
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer version;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean enable;
    
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;
    
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;

    /**
     * 对接产品能力ID标签类型，评估内容ID使用
     */
    @TableField(exist = false)
    @ApiModelProperty("对接产品能力ID标签类型")
    private Integer tagTypeId;
}
