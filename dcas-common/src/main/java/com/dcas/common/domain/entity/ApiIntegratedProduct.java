package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 集成产品实体类
 * 用于存储已集成的第三方产品信息及其状态
 *
 * <AUTHOR>
 */
@Data
@TableName("api_integrated_product")
public class ApiIntegratedProduct {
    
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联API版本记录ID
     */
    @ApiModelProperty("关联API版本记录ID")
    private Long releaseId;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String name;

    /**
     * 产品地址
     */
    @ApiModelProperty("产品地址")
    private String url;
    
    /**
     * 产品能力
     */
    @ApiModelProperty("产品能力")
    private String capability;

    /**
     * 产品类型名称
     */
    @ApiModelProperty("产品类型名称")
    private String productName;
    
    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司ID")
    private String company;
    
    /**
     * 产品logo
     */
    @ApiModelProperty("产品logo")
    private String logo;
    
    /**
     * 集成状态
     * NORMAL_CONNECTION: 正常连接
     * CONNECTION_ERROR: 连接错误
     * PARAMETER_UPDATED: 参数已更新
     * DELETED_FROM_KNOWLEDGE_BASE: 已从知识库删除
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 配置参数（JSON格式）
     */
    @ApiModelProperty("配置参数")
    private String configParams;

    /**
     * 认证配置（JSON格式）
     */
    @ApiModelProperty("认证配置")
    private String authConfig;

    /**
     * 错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMessage;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer version;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
