package com.dcas.common.model.dto;

import com.dcas.common.annotation.CodeEnumValidateAnno;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.enums.SearchLoopholeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/6/21 9:38
 * @ClassName QueryLoopholeDTO
 */
@ApiModel
@Data
public class QueryLoopholeDTO implements Serializable {
    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id", required = true)
    @NotBlank(message = "作业id不能为空")
    private String operationId;

    /**
     * 分类id
     */
    @ApiModelProperty(value = "分类id", required = true)
    @NotNull(message = "分类id不能为空")
    @CodeEnumValidateAnno(enumClass = LabelEnum.class, allowNull = true, message = "不在过程清单范围内")
    private Long labelId;

    /**
     * 漏洞id
     */
    @ApiModelProperty(value = "漏洞id")
    private String loopholeId;

    /**
     * 搜索类型（1=漏洞名称；2=漏洞类型；3=数据库类型；4=所属业务系统；5=危险等级；6=漏洞库编号）
     */
    @ApiModelProperty(value = "搜索类型")
    @CodeEnumValidateAnno(enumClass = SearchLoopholeEnum.class, allowNull = true, message = "不在搜索类型范围内")
    private Integer searchType;

    /**
     * 搜索内容（支持模糊搜索）
     */
    @ApiModelProperty(value = "搜索内容")
    private String searchContent;

}
