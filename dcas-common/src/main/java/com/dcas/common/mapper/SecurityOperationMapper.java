package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.core.domain.BaseEntity;
import com.dcas.common.model.dto.SecurityDetailDTO;
import com.dcas.common.model.dto.SecurityServiceContent;
import com.dcas.common.domain.entity.SecurityOperation;
import com.dcas.common.model.dto.TemplateServiceContentDTO;
import com.dcas.common.model.req.SecuritySearchReq;
import com.dcas.common.model.vo.SecurityDetailVO;
import com.dcas.common.model.vo.SecurityWorkVO;
import org.apache.ibatis.annotations.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/3 15:16
 * @since 1.4.0
 */
public interface SecurityOperationMapper extends BaseMapper<SecurityOperation> {

    /**
     * 分页查询安全作业
     *
     * @param req 安全作业查询参数
     * @return 安全作业分页结果
     */
    List<SecurityWorkVO> pageQuery(SecuritySearchReq req);

    void updateStateById(@Param("id") Integer id, @Param("status") Byte status);

    List<String> selectFileIdsBySecurityId(@Param("id") Integer id);

    List<SecurityServiceContent> selectServiceBySecurityId(@Param("id") Integer id);

    SecurityDetailVO selectDetailById(@Param("id") Integer id);

    List<SecurityOperation> selectOperationList(BaseEntity entity);

    List<Integer> selectOperationIdsByProjectId(@Param("projectId") String projectId);

    @Results({
        @Result(property = "key", column = "key"),
        @Result(property = "value", column = "value")
    })
    @Select("(select key, value from template_service_content where type = #{type} and usage = #{usage} order by sort asc)" +
        "union all \n" +
        "(select (t.type_id || a.capability)::INTEGER as key,t.name as value from api_release a inner join tag t on t.id::varchar = a.capability where a.enable = true order by a.capability)")
    List<Map<Integer, String>> selectServiceContent(@Param("type") int type, @Param("usage") int usage);

    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "parentId", column = "parent_id"),
        @Result(property = "key", column = "key"),
        @Result(property = "value", column = "value"),
        @Result(property = "sort", column = "sort")
    })
    @Select("(select id, parent_id, key, value, sort from template_service_content where type = #{type} and usage = 2 order by sort asc)" +
        "union all \n" +
        "(select t.id as id, 3 as parent_id, (t.type_id || a.capability)::INTEGER as key,t.name as value, t.id as sort from api_release a inner join tag t on t.id::varchar = a.capability where a.enable = true order by a.capability)")
    List<TemplateServiceContentDTO> selectServiceContentByType(@Param("type") int type);

    List<SecurityDetailDTO> selectDetailByIdAndModel(@Param("id") Integer id, @Param("isPreview") boolean isPreview);

    void updateCheckedStatus(@Param("list") List<Integer> checkedIds, @Param("checked") boolean checked);

    int labelIsExist(@Param("securityId") Integer securityId, @Param("labelId") Long labelId);

    void updateByBatchIds(@Param("ids") List<Integer> ids);

    @Select("select distinct t.id from api_release a inner join tag t on t.id::varchar = a.capability where a.enable = true")
    List<Integer> selectProductServiceContent();
}
