package com.dcas.common.model.other;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 键值对VO
 *
 * <AUTHOR>
 * @date 2023/08/28 14:03
 **/
@Data
public class KeyValue {

    @ApiModelProperty(name = "键")
    private Object key;
    @ApiModelProperty(name = "值")
    private String value;

    public KeyValue(){}
    public KeyValue(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public KeyValue(Long key, String value) {
        this.key = key;
        this.value = value;
    }

    public KeyValue(Integer key, String value) {
        this.key = key;
        this.value = value;
    }
}
