package com.dcas.common.model.param;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/7/9 14:09
 * @since 2.1.3.0
 */
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "type"
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = SystemAutoParameter.class, name = "SYSTEM_AUTO"),
        @JsonSubTypes.Type(value = AlgorithmParameter.class, name = "ALGORITHM")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthParameter {

    /**
     * 参数键名
     */
    private String key;

    /**
     * 参数显示名称
     */
    private String name;

    /**
     * 参数类型
     */
    private String type;

    /**
     * 是否放在请求头中
     */
    private Boolean inHeader;
}
